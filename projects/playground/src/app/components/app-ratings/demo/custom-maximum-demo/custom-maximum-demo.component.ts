import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AavaRatingComponent } from '../../../../../../../play-comp-library/src/lib/components/rating/aava-rating.component';

@Component({
  selector: 'ava-rating-custom-maximum-demo',
  standalone: true,
  imports: [CommonModule, AavaRatingComponent],
  template: `
    <div class="demo-container">
      <div class="demo-description">
        <h3>Custom Maximum</h3>
        <p>Flexible rating scales beyond the default 5-star system.</p>
      </div>

      <div class="custom-max-demo">
        <div class="demo-section">
          <h4>Different Rating Scales</h4>
          <div class="scale-examples">
            <div class="scale-example">
              <h5>3-Star Scale</h5>
              <aava-rating
                [value]="rating3Star"
                [max]="3"
                (rated)="onRating3StarChange($event)"
              ></aava-rating>
              <span class="rating-value">{{ rating3Star }} out of 3</span>
            </div>

            <div class="scale-example">
              <h5>4-Star Scale</h5>
              <aava-rating
                [value]="rating4Star"
                [max]="4"
                (rated)="onRating4StarChange($event)"
              ></aava-rating>
              <span class="rating-value">{{ rating4Star }} out of 4</span>
            </div>

            <div class="scale-example">
              <h5>5-Star Scale (Default)</h5>
              <aava-rating
                [value]="rating5Star"
                [max]="5"
                (rated)="onRating5StarChange($event)"
              ></aava-rating>
              <span class="rating-value">{{ rating5Star }} out of 5</span>
            </div>

            <div class="scale-example">
              <h5>10-Star Scale</h5>
              <aava-rating
                [value]="rating10Star"
                [max]="10"
                (rated)="onRating10StarChange($event)"
              ></aava-rating>
              <span class="rating-value">{{ rating10Star }} out of 10</span>
            </div>
          </div>
        </div>

        <div class="demo-section">
          <h4>Interactive Custom Scale</h4>
          <div class="custom-scale-controls">
            <div class="scale-input">
              <label for="maxInput">Maximum Stars:</label>
              <input
                type="number"
                id="maxInput"
                [value]="customMax"
                (input)="onMaxChange($event)"
                min="1"
                max="20"
                class="max-input"
              />
            </div>

            <div class="rating-display">
              <aava-rating
                [value]="customRating"
                [max]="customMax"
                (rated)="onCustomRatingChange($event)"
                [showValue]="true"
              ></aava-rating>
              <p>Rating: {{ customRating }} out of {{ customMax }}</p>
            </div>
          </div>
        </div>

        <div class="demo-section">
          <h4>Half-Star Support with Custom Scales</h4>
          <div class="half-star-examples">
            <div class="half-star-example">
              <span class="example-label">3-Star Scale (2.5):</span>
              <aava-rating
                [value]="2.5"
                [max]="3"
                [readonly]="true"
              ></aava-rating>
            </div>
            <div class="half-star-example">
              <span class="example-label">4-Star Scale (3.5):</span>
              <aava-rating
                [value]="3.5"
                [max]="4"
                [readonly]="true"
              ></aava-rating>
            </div>
            <div class="half-star-example">
              <span class="example-label">10-Star Scale (7.5):</span>
              <aava-rating
                [value]="7.5"
                [max]="10"
                [readonly]="true"
              ></aava-rating>
            </div>
          </div>
        </div>
      </div>

      <div class="rating-controls">
        <button (click)="resetAllRatings()" class="reset-btn">
          Reset All Ratings
        </button>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
        margin-top: 3rem;
        text-align: center;
      }

      .demo-description {
        margin-bottom: 2rem;
      }

      .demo-description h3 {
        color: #333;
        margin-bottom: 1rem;
        font-size: 24px;
      }

      .demo-description p {
        color: #666;
        font-size: 16px;
        line-height: 1.5;
      }

      .custom-max-demo {
        margin-bottom: 2rem;
      }

      .demo-section {
        padding: 2rem;
        background-color: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 2rem;
        text-align: center;
      }

      .demo-section h4 {
        color: #333;
        margin-bottom: 1.5rem;
        font-size: 20px;
        font-weight: 500;
      }

      .scale-examples {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 2rem;
        margin-bottom: 1rem;
      }

      .scale-example {
        padding: 1.5rem;
        background-color: white;
        border-radius: 6px;
        border: 1px solid #dee2e6;
        text-align: center;
      }

      .scale-example h5 {
        color: #333;
        margin-bottom: 1rem;
        font-size: 16px;
        font-weight: 500;
      }

      .rating-value {
        display: block;
        margin-top: 1rem;
        color: #666;
        font-size: 14px;
        font-weight: 500;
      }

      .custom-scale-controls {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2rem;
      }

      .scale-input {
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .scale-input label {
        font-size: 16px;
        color: #333;
        font-weight: 500;
      }

      .max-input {
        padding: 8px 12px;
        border: 2px solid #dee2e6;
        border-radius: 4px;
        font-size: 16px;
        width: 80px;
        text-align: center;
      }

      .max-input:focus {
        outline: none;
        border-color: #007bff;
      }

      .rating-display {
        text-align: center;
      }

      .rating-display p {
        margin-top: 1rem;
        font-size: 18px;
        color: #333;
        font-weight: 500;
      }

      .half-star-examples {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
      }

      .half-star-example {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        background-color: white;
        border-radius: 6px;
        border: 1px solid #dee2e6;
        justify-content: center;
      }

      .example-label {
        font-size: 14px;
        color: #666;
        font-weight: 500;
        min-width: 150px;
        text-align: left;
      }

      .rating-controls {
        text-align: center;
      }

      .reset-btn {
        background-color: #dc3545;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 500;
      }

      .reset-btn:hover {
        background-color: #c82333;
      }
    `,
  ],
})
export class CustomMaximumDemoComponent {
  rating3Star = 0;
  rating4Star = 0;
  rating5Star = 0;
  rating10Star = 0;
  customMax = 7;
  customRating = 0;

  onRating3StarChange(value: number) {
    this.rating3Star = value;
    console.log('3-star rating changed to:', value);
  }

  onRating4StarChange(value: number) {
    this.rating4Star = value;
    console.log('4-star rating changed to:', value);
  }

  onRating5StarChange(value: number) {
    this.rating5Star = value;
    console.log('5-star rating changed to:', value);
  }

  onRating10StarChange(value: number) {
    this.rating10Star = value;
    console.log('10-star rating changed to:', value);
  }

  onMaxChange(event: Event) {
    const target = event.target as HTMLInputElement;
    const value = parseInt(target.value);
    if (value >= 1 && value <= 20) {
      this.customMax = value;
      // Adjust rating if it exceeds new max
      if (this.customRating > this.customMax) {
        this.customRating = this.customMax;
      }
    }
  }

  onCustomRatingChange(value: number) {
    this.customRating = value;
    console.log('Custom rating changed to:', value);
  }

  resetAllRatings() {
    this.rating3Star = 0;
    this.rating4Star = 0;
    this.rating5Star = 0;
    this.rating10Star = 0;
    this.customRating = 0;
  }
}
