import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AavaRatingComponent } from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-rating-half-star-demo',
  standalone: true,
  imports: [CommonModule, AavaRatingComponent],
  template: `
    <div class="demo-container">
      <div class="demo-description">
        <h3>Half-Star Ratings</h3>
        <p>
          Support for precise half-star ratings (e.g., 4.5 stars) with intuitive
          click positioning.
        </p>
      </div>

      <div class="half-star-demo">
        <div class="demo-section">
          <h4>Interactive Half-Star Rating</h4>
          <p class="instruction">
            Click on the left half of a star for half rating, right half for
            full rating
          </p>

          <aava-rating
            [value]="ratingValue"
            (rated)="onRatingChange($event)"
          ></aava-rating>

          <div class="rating-display">
            <p>
              Current Rating: <strong>{{ ratingValue }}</strong>
            </p>
            <p class="rating-text">{{ getRatingText(ratingValue) }}</p>
          </div>
        </div>

        <div class="demo-section">
          <h4>Half-Star Examples</h4>
          <div class="examples-grid">
            <div class="example-item">
              <span class="example-label">3.5 Stars:</span>
              <aava-rating [value]="3.5" [readonly]="true"></aava-rating>
            </div>
            <div class="example-item">
              <span class="example-label">4.5 Stars:</span>
              <aava-rating [value]="4.5" [readonly]="true"></aava-rating>
            </div>
            <div class="example-item">
              <span class="example-label">2.5 Stars:</span>
              <aava-rating [value]="2.5" [readonly]="true"></aava-rating>
            </div>
          </div>
        </div>
      </div>

      <div class="rating-controls">
        <button (click)="setRating(1.5)" class="control-btn">Set 1.5</button>
        <button (click)="setRating(2.5)" class="control-btn">Set 2.5</button>
        <button (click)="setRating(3.5)" class="control-btn">Set 3.5</button>
        <button (click)="setRating(4.5)" class="control-btn">Set 4.5</button>
        <button (click)="resetRating()" class="reset-btn">Reset</button>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
        margin-top: 3rem;
        text-align: center;
      }

      .demo-description {
        margin-bottom: 2rem;
      }

      .demo-description h3 {
        color: #333;
        margin-bottom: 1rem;
        font-size: 24px;
      }

      .demo-description p {
        color: #666;
        font-size: 16px;
        line-height: 1.5;
      }

      .half-star-demo {
        margin-bottom: 2rem;
      }

      .demo-section {
        padding: 2rem;
        background-color: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 2rem;
        text-align: center;
      }

      .demo-section h4 {
        color: #333;
        margin-bottom: 1rem;
        font-size: 20px;
        font-weight: 500;
      }

      .instruction {
        color: #666;
        font-size: 14px;
        margin-bottom: 1.5rem;
        font-style: italic;
      }

      .rating-display {
        margin-top: 1.5rem;
      }

      .rating-display p {
        margin: 0.5rem 0;
        font-size: 16px;
      }

      .rating-text {
        color: #007bff;
        font-weight: 500;
      }

      .examples-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-top: 1.5rem;
      }

      .example-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem;
        background-color: white;
        border-radius: 6px;
        border: 1px solid #dee2e6;
      }

      .example-label {
        font-size: 14px;
        color: #666;
        font-weight: 500;
      }

      .rating-controls {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
        flex-wrap: wrap;
      }

      .control-btn {
        background-color: #6c757d;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
      }

      .control-btn:hover {
        background-color: #5a6268;
      }

      .reset-btn {
        background-color: #dc3545;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
      }

      .reset-btn:hover {
        background-color: #c82333;
      }
    `,
  ],
})
export class HalfStarDemoComponent {
  ratingValue = 0;

  onRatingChange(value: number) {
    this.ratingValue = value;
    console.log('Rating changed to:', value);
  }

  setRating(value: number) {
    this.ratingValue = value;
  }

  resetRating() {
    this.ratingValue = 0;
  }

  getRatingText(rating: number): string {
    if (rating === 0) return 'No rating';
    if (rating === 0.5) return 'Half star';
    if (rating === Math.floor(rating)) return `${rating} stars`;
    return `${Math.floor(rating)}.5 stars`;
  }
}
