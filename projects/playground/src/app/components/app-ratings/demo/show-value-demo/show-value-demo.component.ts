import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AavaRatingComponent } from '../../../../../../../play-comp-library/src/lib/components/rating/aava-rating.component';

@Component({
  selector: 'ava-rating-show-value-demo',
  standalone: true,
  imports: [CommonModule, AavaRatingComponent],
  template: `
    <div class="demo-container">
      <div class="demo-description">
        <h3>Show Value</h3>
        <p>Display the numeric rating value alongside the visual stars.</p>
      </div>

      <div class="show-value-demo">
        <div class="demo-section">
          <h4>Interactive Rating with Value Display</h4>
          <aava-rating
            [value]="ratingValue"
            [showValue]="true"
            (rated)="onRatingChange($event)"
          ></aava-rating>

          <div class="rating-info">
            <p>
              Current Rating: <strong>{{ ratingValue }}</strong>
            </p>
          </div>
        </div>

        <div class="demo-section">
          <h4>Different Sizes with Values</h4>
          <div class="size-examples">
            <div class="size-example">
              <span class="size-label">Small:</span>
              <aava-rating
                [value]="4.5"
                size="sm"
                [showValue]="true"
              ></aava-rating>
            </div>
            <div class="size-example">
              <span class="size-label">Medium:</span>
              <aava-rating
                [value]="4.5"
                size="md"
                [showValue]="true"
              ></aava-rating>
            </div>
            <div class="size-example">
              <span class="size-label">Large:</span>
              <aava-rating
                [value]="4.5"
                size="lg"
                [showValue]="true"
              ></aava-rating>
            </div>
          </div>
        </div>

        <div class="demo-section">
          <h4>Various Rating Values</h4>
          <div class="rating-examples">
            <div class="rating-example">
              <aava-rating [value]="5.0" [showValue]="true"></aava-rating>
              <span class="rating-label">Perfect</span>
            </div>
            <div class="rating-example">
              <aava-rating [value]="4.5" [showValue]="true"></aava-rating>
              <span class="rating-label">Excellent</span>
            </div>
            <div class="rating-example">
              <aava-rating [value]="4.0" [showValue]="true"></aava-rating>
              <span class="rating-label">Very Good</span>
            </div>
            <div class="rating-example">
              <aava-rating [value]="3.5" [showValue]="true"></aava-rating>
              <span class="rating-label">Good</span>
            </div>
            <div class="rating-example">
              <aava-rating [value]="3.0" [showValue]="true"></aava-rating>
              <span class="rating-label">Average</span>
            </div>
            <div class="rating-example">
              <aava-rating [value]="2.0" [showValue]="true"></aava-rating>
              <span class="rating-label">Below Average</span>
            </div>
          </div>
        </div>
      </div>

      <div class="rating-controls">
        <button (click)="setRating(1.0)" class="control-btn">Set 1.0</button>
        <button (click)="setRating(2.5)" class="control-btn">Set 2.5</button>
        <button (click)="setRating(3.0)" class="control-btn">Set 3.0</button>
        <button (click)="setRating(4.5)" class="control-btn">Set 4.5</button>
        <button (click)="setRating(5.0)" class="control-btn">Set 5.0</button>
        <button (click)="resetRating()" class="reset-btn">Reset</button>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
        margin-top: 3rem;
        text-align: center;
      }

      .demo-description {
        margin-bottom: 2rem;
      }

      .demo-description h3 {
        color: #333;
        margin-bottom: 1rem;
        font-size: 24px;
      }

      .demo-description p {
        color: #666;
        font-size: 16px;
        line-height: 1.5;
      }

      .show-value-demo {
        margin-bottom: 2rem;
      }

      .demo-section {
        padding: 2rem;
        background-color: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 2rem;
        text-align: center;
      }

      .demo-section h4 {
        color: #333;
        margin-bottom: 1.5rem;
        font-size: 20px;
        font-weight: 500;
      }

      .rating-info {
        margin-top: 1.5rem;
      }

      .rating-info p {
        font-size: 18px;
        color: #333;
        font-weight: 500;
      }

      .size-examples {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        align-items: center;
      }

      .size-example {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        background-color: white;
        border-radius: 6px;
        border: 1px solid #dee2e6;
        min-width: 300px;
      }

      .size-label {
        font-size: 14px;
        color: #666;
        font-weight: 500;
        min-width: 80px;
        text-align: left;
      }

      .rating-examples {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
      }

      .rating-example {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        padding: 1.5rem;
        background-color: white;
        border-radius: 6px;
        border: 1px solid #dee2e6;
      }

      .rating-label {
        font-size: 14px;
        color: #666;
        font-weight: 500;
      }

      .rating-controls {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
        flex-wrap: wrap;
      }

      .control-btn {
        background-color: #6c757d;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
      }

      .control-btn:hover {
        background-color: #5a6268;
      }

      .reset-btn {
        background-color: #dc3545;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
      }

      .reset-btn:hover {
        background-color: #c82333;
      }
    `,
  ],
})
export class ShowValueDemoComponent {
  ratingValue = 0;

  onRatingChange(value: number) {
    this.ratingValue = value;
    console.log('Rating changed to:', value);
  }

  setRating(value: number) {
    this.ratingValue = value;
  }

  resetRating() {
    this.ratingValue = 0;
  }
}
