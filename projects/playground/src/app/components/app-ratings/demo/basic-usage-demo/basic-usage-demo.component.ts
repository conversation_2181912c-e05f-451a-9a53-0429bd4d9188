import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AavaRatingComponent } from '../../../../../../../play-comp-library/src/lib/components/rating/aava-rating.component';

@Component({
  selector: 'ava-rating-basic-usage-demo',
  standalone: true,
  imports: [CommonModule, AavaRatingComponent],
  template: `
    <div class="demo-container">
      <div class="rating-demo">
        <aava-rating
          [value]="ratingValue"
          (rated)="onRatingChange($event)"
        ></aava-rating>
        <p style="color: var(--color-text-primary)">
          Rating: {{ ratingValue }}
        </p>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 800px;
        margin: 0 auto;
        margin-top: 3rem;
        text-align: center;
      }

      .demo-description {
        margin-bottom: 2rem;
      }

      .demo-description h3 {
        color: #333;
        margin-bottom: 1rem;
        font-size: 24px;
      }

      .demo-description p {
        color: #666;
        font-size: 16px;
        line-height: 1.5;
      }

      .rating-demo {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        border-radius: 8px;
      }

      .rating-info {
        text-align: center;
      }

      .rating-info p {
        font-size: 18px;
        color: #333;
        margin-bottom: 1rem;
        font-weight: 500;
      }

      .reset-btn {
        background-color: #007bff;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
      }

      .reset-btn:hover {
        background-color: #0056b3;
      }
    `,
  ],
})
export class BasicUsageDemoComponent {
  ratingValue = 0;

  onRatingChange(value: number) {
    this.ratingValue = value;
    console.log('Rating changed to:', value);
  }

  resetRating() {
    this.ratingValue = 0;
  }
}
