<div class="sidebar-demo">
  <!-- Hero Section -->
  <section class="hero-section">
    <div class="hero-content">
      <h1>Sidebar Component</h1>
      <p>A flexible sidebar component with support for left/right positioning, collapsible states, and customizable content areas.</p>
      <div class="scroll-indicator" (click)="scrollToFirstDemo()">
        <span>Scroll to explore</span>
        <ava-icon iconName="ChevronDown"></ava-icon>
      </div>
    </div>
  </section>

  <!-- Left Sidebar with Inside Button -->
  <section class="layout-section">
    <div class="section-header">
      <h2>Left Sidebar + Inside Button</h2>
      <p>Traditional sidebar layout with collapse button inside the sidebar</p>
    </div>

    <div class="full-layout">
      <div class="demo-header-bar">
        <div class="header-left">
          <h3>Dashboard Application</h3>
        </div>
        <div class="header-right">
          <ava-button label="Profile" variant="secondary" size="small"></ava-button>
          <ava-button label="Settings" variant="primary" size="small"></ava-button>
        </div>
      </div>

      <div class="layout-content">
        <ava-sidebar
          width="280px"
          collapsedWidth="70px"
          [height]="'calc(100vh - 120px)'"
          [showCollapseButton]="true"
          buttonVariant="inside"
          [showHeader]="true"
          [showFooter]="true"
          position="left"
          [isCollapsed]="leftSidebarCollapsed"
          (collapseToggle)="onLeftSidebarToggle($event)"
        >
          <div slot="header" class="demo-header-content">
            <ava-icon iconName="Menu"></ava-icon>
            <span class="header-title">Navigation</span>
          </div>
          <div slot="content" class="demo-content">
            <div class="nav-item active">
              <ava-icon iconName="Home"></ava-icon>
              <span>Dashboard</span>
            </div>
            <div class="nav-item">
              <ava-icon iconName="Users"></ava-icon>
              <span>Users</span>
            </div>
            <div class="nav-item">
              <ava-icon iconName="FileText"></ava-icon>
              <span>Projects</span>
            </div>
            <div class="nav-item">
              <ava-icon iconName="ChartBar"></ava-icon>
              <span>Analytics</span>
            </div>
            <div class="nav-item">
              <ava-icon iconName="Settings"></ava-icon>
              <span>Settings</span>
            </div>
          </div>
          <div slot="footer" class="demo-footer-content">
            <div class="user-info">
              <ava-icon iconName="User"></ava-icon>
              <span>John Doe</span>
            </div>
          </div>
        </ava-sidebar>

        <div class="main-content">
          <div class="content-header">
            <h2>Welcome to Dashboard</h2>
            <p>Inside button variant provides easy access to collapse functionality</p>
          </div>
          <div class="content-body">
            <div class="content-card">
              <h3>Main Content Area</h3>
              <p>This is where your main application content would go. The sidebar can be collapsed to give more space to the content using the button inside the sidebar header.</p>
              <div class="stats-grid">
                <div class="stat-item">
                  <ava-icon iconName="Users"></ava-icon>
                  <div class="stat-info">
                    <span class="stat-number">1,234</span>
                    <span class="stat-label">Users</span>
                  </div>
                </div>
                <div class="stat-item">
                  <ava-icon iconName="FileText"></ava-icon>
                  <div class="stat-info">
                    <span class="stat-number">567</span>
                    <span class="stat-label">Projects</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Right Sidebar with Inside Button -->
  <section class="layout-section">
    <div class="section-header">
      <h2>Right Sidebar + Inside Button</h2>
      <p>Right-positioned sidebar for secondary navigation and tools</p>
    </div>

    <div class="full-layout">
      <div class="demo-header-bar">
        <div class="header-left">
          <h3>Content Editor</h3>
        </div>
        <div class="header-right">
          <ava-button label="Save" variant="primary" size="small"></ava-button>
          <ava-button label="Preview" variant="secondary" size="small"></ava-button>
        </div>
      </div>

      <div class="layout-content">
        <div class="main-content">
          <div class="content-header">
            <h2>Document Editor</h2>
            <p>Right sidebar provides tools and options without interfering with main content</p>
          </div>
          <div class="content-body">
            <div class="content-card">
              <h3>Editor Area</h3>
              <p>The main editing area where users can create and modify content. The right sidebar contains tools, properties, and actions that complement the editing experience.</p>
              <div class="editor-placeholder">
                <p>📝 Your content goes here...</p>
                <p>The right sidebar contains all the tools you need to format and enhance your content.</p>
              </div>
            </div>
          </div>
        </div>

        <ava-sidebar
          width="280px"
          collapsedWidth="70px"
          [height]="'calc(100vh - 120px)'"
          [showCollapseButton]="true"
          buttonVariant="inside"
          [showHeader]="true"
          [showFooter]="true"
          position="right"
          [isCollapsed]="rightSidebarCollapsed"
          (collapseToggle)="onRightSidebarToggle($event)"
        >
          <div slot="header" class="demo-header-content">
            <ava-icon iconName="Settings"></ava-icon>
            <span class="header-title">Tools</span>
          </div>
          <div slot="content" class="demo-content">
            <div class="nav-item active">
              <ava-icon iconName="Palette"></ava-icon>
              <span>Themes</span>
            </div>
            <div class="nav-item">
              <ava-icon iconName="Layers"></ava-icon>
              <span>Layers</span>
            </div>
            <div class="nav-item">
              <ava-icon iconName="Image"></ava-icon>
              <span>Assets</span>
            </div>
            <div class="nav-item">
              <ava-icon iconName="Type"></ava-icon>
              <span>Typography</span>
            </div>
            <div class="nav-item">
              <ava-icon iconName="Share2"></ava-icon>
              <span>Export</span>
            </div>
          </div>
          <div slot="footer" class="demo-footer-content">
            <ava-button label="Apply Changes" variant="primary" size="small"></ava-button>
          </div>
        </ava-sidebar>
      </div>
    </div>
  </section>

  <!-- Left Sidebar with Outside Button -->
  <section class="layout-section">
    <div class="section-header">
      <h2>Left Sidebar + Outside Button</h2>
      <p>Clean design with hover-triggered collapse button positioned outside the sidebar</p>
    </div>

    <div class="full-layout">
      <div class="demo-header-bar">
        <div class="header-left">
          <h3>File Manager Pro</h3>
        </div>
        <div class="header-right">
          <ava-button label="Upload" variant="primary" size="small"></ava-button>
          <ava-button label="New Folder" variant="secondary" size="small"></ava-button>
        </div>
      </div>

      <div class="layout-content">
        <ava-sidebar
          width="320px"
          collapsedWidth="80px"
          [height]="'calc(100vh - 120px)'"
          [showCollapseButton]="true"
          buttonVariant="outside"
          [showHeader]="true"
          [showFooter]="true"
          position="left"
          [isCollapsed]="customSizeCollapsed"
          (collapseToggle)="onCustomSizeToggle($event)"
          hoverAreaWidth="40px"
        >
          <div slot="header" class="demo-header-content">
            <ava-icon iconName="Folder"></ava-icon>
            <span class="header-title">File Explorer</span>
          </div>
          <div slot="content" class="demo-content">
            <div class="nav-item active">
              <ava-icon iconName="Home"></ava-icon>
              <span>Home</span>
            </div>
            <div class="nav-item">
              <ava-icon iconName="Folder"></ava-icon>
              <span>Documents</span>
            </div>
            <div class="nav-item">
              <ava-icon iconName="Image"></ava-icon>
              <span>Pictures</span>
            </div>
            <div class="nav-item">
              <ava-icon iconName="SquarePlay"></ava-icon>
              <span>Videos</span>
            </div>
            <div class="nav-item">
              <ava-icon iconName="Download"></ava-icon>
              <span>Downloads</span>
            </div>
            <div class="nav-item">
              <ava-icon iconName="Archive"></ava-icon>
              <span>Archive</span>
            </div>
          </div>
          <div slot="footer" class="demo-footer-content">
            <div class="storage-info">
              <small>Storage: 45.2 GB / 100 GB</small>
              <div class="storage-bar">
                <div class="storage-used" style="width: 45%"></div>
              </div>
            </div>
          </div>
        </ava-sidebar>

        <div class="main-content">
          <div class="content-header">
            <h2>File Explorer</h2>
            <p>Hover over the right edge of the sidebar to see the collapse button</p>
          </div>
          <div class="content-body">
            <div class="content-card">
              <h3>Outside Button Variant</h3>
              <p>The collapse button appears in a hover area outside the sidebar. This provides a cleaner look while still allowing easy access to the collapse functionality.</p>
              <div class="file-grid">
                <div class="file-item">
                  <ava-icon iconName="Folder"></ava-icon>
                  <span>Projects</span>
                </div>
                <div class="file-item">
                  <ava-icon iconName="FileText"></ava-icon>
                  <span>Document.pdf</span>
                </div>
                <div class="file-item">
                  <ava-icon iconName="Image"></ava-icon>
                  <span>Photo.jpg</span>
                </div>
                <div class="file-item">
                  <ava-icon iconName="SquarePlay"></ava-icon>
                  <span>Video.mp4</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Static Sidebar -->
  <section class="layout-section">
    <div class="section-header">
      <h2>Static Sidebar</h2>
      <p>Always visible navigation without collapse functionality - perfect for consistent navigation</p>
    </div>

    <div class="full-layout">
      <div class="demo-header-bar">
        <div class="header-left">
          <h3>Admin Dashboard</h3>
        </div>
        <div class="header-right">
          <ava-button label="Notifications" variant="secondary" size="small"></ava-button>
          <ava-button label="Profile" variant="primary" size="small"></ava-button>
        </div>
      </div>

      <div class="layout-content">
        <ava-sidebar
          width="280px"
          [height]="'calc(100vh - 120px)'"
          [showCollapseButton]="false"
          [showHeader]="true"
          [showFooter]="true"
          position="left"
        >
          <div slot="header" class="demo-header-content">
            <ava-icon iconName="ChartBar"></ava-icon>
            <span class="header-title">Analytics</span>
          </div>
          <div slot="content" class="demo-content">
            <div class="nav-item active">
              <ava-icon iconName="Home"></ava-icon>
              <span>Overview</span>
            </div>
            <div class="nav-item">
              <ava-icon iconName="ChartBar"></ava-icon>
              <span>Analytics</span>
            </div>
            <div class="nav-item">
              <ava-icon iconName="Users"></ava-icon>
              <span>Customers</span>
            </div>
            <div class="nav-item">
              <ava-icon iconName="ShoppingCart"></ava-icon>
              <span>Orders</span>
            </div>
            <div class="nav-item">
              <ava-icon iconName="Package"></ava-icon>
              <span>Products</span>
            </div>
            <div class="nav-item">
              <ava-icon iconName="CreditCard"></ava-icon>
              <span>Payments</span>
            </div>
            <div class="nav-item">
              <ava-icon iconName="Settings"></ava-icon>
              <span>Settings</span>
            </div>
          </div>
          <div slot="footer" class="demo-footer-content">
            <div class="user-info">
              <ava-icon iconName="User"></ava-icon>
              <span>Admin User</span>
            </div>
          </div>
        </ava-sidebar>

        <div class="main-content">
          <div class="content-header">
            <h2>Analytics Dashboard</h2>
            <p>Static sidebar provides consistent navigation without complexity</p>
          </div>
          <div class="content-body">
            <div class="content-card">
              <h3>Dashboard Overview</h3>
              <p>A static sidebar is perfect when you want consistent, always-visible navigation without the complexity of collapse functionality.</p>
              <div class="dashboard-grid">
                <div class="dashboard-item">
                  <ava-icon iconName="Users"></ava-icon>
                  <div class="dashboard-info">
                    <span class="dashboard-number">2,543</span>
                    <span class="dashboard-label">Total Users</span>
                  </div>
                </div>
                <div class="dashboard-item">
                  <ava-icon iconName="ShoppingCart"></ava-icon>
                  <div class="dashboard-info">
                    <span class="dashboard-number">1,234</span>
                    <span class="dashboard-label">Orders</span>
                  </div>
                </div>
                <div class="dashboard-item">
                  <ava-icon iconName="CreditCard"></ava-icon>
                  <div class="dashboard-info">
                    <span class="dashboard-number">$45,678</span>
                    <span class="dashboard-label">Revenue</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- All Variants Grid Section -->
  <section class="variants-section">
    <div class="section-header">
      <h2>All Sidebar Variants</h2>
      <p>Compare all sidebar configurations side by side</p>
    </div>

    <div class="variants-grid">
      <!-- Left Sidebar with Inside Button -->
      <div class="variant-item">
        <div class="variant-header">
          <h3>Left + Inside Button</h3>
          <p>Traditional sidebar with collapse button inside</p>
        </div>
        <div class="variant-demo">
          <ava-sidebar
            width="240px"
            collapsedWidth="60px"
            height="350px"
            [showCollapseButton]="true"
            buttonVariant="inside"
            [showHeader]="true"
            [showFooter]="true"
            position="left"
            [isCollapsed]="leftSidebarCollapsed"
            (collapseToggle)="onLeftSidebarToggle($event)"
          >
            <div slot="header" class="demo-header-content">
              <ava-icon iconName="Menu"></ava-icon>
              <span class="header-title">Navigation</span>
            </div>
            <div slot="content" class="demo-content">
              <div class="nav-item active">
                <ava-icon iconName="Home"></ava-icon>
                <span>Dashboard</span>
              </div>
              <div class="nav-item">
                <ava-icon iconName="Users"></ava-icon>
                <span>Users</span>
              </div>
              <div class="nav-item">
                <ava-icon iconName="FileText"></ava-icon>
                <span>Projects</span>
              </div>
              <div class="nav-item">
                <ava-icon iconName="Settings"></ava-icon>
                <span>Settings</span>
              </div>
            </div>
            <div slot="footer" class="demo-footer-content">
              <div class="user-info">
                <ava-icon iconName="User"></ava-icon>
                <span>Admin</span>
              </div>
            </div>
          </ava-sidebar>
        </div>
      </div>

      <!-- Right Sidebar with Inside Button -->
      <div class="variant-item">
        <div class="variant-header">
          <h3>Right + Inside Button</h3>
          <p>Right-positioned sidebar for secondary navigation</p>
        </div>
        <div class="variant-demo">
          <ava-sidebar
            width="240px"
            collapsedWidth="60px"
            height="350px"
            [showCollapseButton]="true"
            buttonVariant="inside"
            [showHeader]="true"
            [showFooter]="true"
            position="right"
            [isCollapsed]="rightSidebarCollapsed"
            (collapseToggle)="onRightSidebarToggle($event)"
          >
            <div slot="header" class="demo-header-content">
              <ava-icon iconName="Settings"></ava-icon>
              <span class="header-title">Tools</span>
            </div>
            <div slot="content" class="demo-content">
              <div class="nav-item active">
                <ava-icon iconName="Palette"></ava-icon>
                <span>Themes</span>
              </div>
              <div class="nav-item">
                <ava-icon iconName="Layers"></ava-icon>
                <span>Layers</span>
              </div>
              <div class="nav-item">
                <ava-icon iconName="Image"></ava-icon>
                <span>Assets</span>
              </div>
              <div class="nav-item">
                <ava-icon iconName="Share2"></ava-icon>
                <span>Export</span>
              </div>
            </div>
            <div slot="footer" class="demo-footer-content">
              <ava-button label="Apply" variant="primary" size="small"></ava-button>
            </div>
          </ava-sidebar>
        </div>
      </div>

      <!-- Left Sidebar with Outside Button -->
      <div class="variant-item">
        <div class="variant-header">
          <h3>Left + Outside Button</h3>
          <p>Clean design with hover-triggered collapse button</p>
        </div>
        <div class="variant-demo">
          <ava-sidebar
            width="240px"
            collapsedWidth="60px"
            height="350px"
            [showCollapseButton]="true"
            buttonVariant="outside"
            [showHeader]="true"
            [showFooter]="true"
            position="left"
            [isCollapsed]="customSizeCollapsed"
            (collapseToggle)="onCustomSizeToggle($event)"
            hoverAreaWidth="30px"
          >
            <div slot="header" class="demo-header-content">
              <ava-icon iconName="Folder"></ava-icon>
              <span class="header-title">Explorer</span>
            </div>
            <div slot="content" class="demo-content">
              <div class="nav-item active">
                <ava-icon iconName="Home"></ava-icon>
                <span>Home</span>
              </div>
              <div class="nav-item">
                <ava-icon iconName="Folder"></ava-icon>
                <span>Documents</span>
              </div>
              <div class="nav-item">
                <ava-icon iconName="Image"></ava-icon>
                <span>Pictures</span>
              </div>
              <div class="nav-item">
                <ava-icon iconName="Download"></ava-icon>
                <span>Downloads</span>
              </div>
            </div>
            <div slot="footer" class="demo-footer-content">
              <div class="storage-info">
                <small>Storage: 45GB / 100GB</small>
                <div class="storage-bar">
                  <div class="storage-used" style="width: 45%"></div>
                </div>
              </div>
            </div>
          </ava-sidebar>
        </div>
      </div>

      <!-- Static Sidebar -->
      <div class="variant-item">
        <div class="variant-header">
          <h3>Static Sidebar</h3>
          <p>Always visible navigation without collapse functionality</p>
        </div>
        <div class="variant-demo">
          <ava-sidebar
            width="240px"
            height="350px"
            [showCollapseButton]="false"
            [showHeader]="true"
            [showFooter]="true"
            position="left"
          >
            <div slot="header" class="demo-header-content">
              <ava-icon iconName="ChartBar"></ava-icon>
              <span class="header-title">Analytics</span>
            </div>
            <div slot="content" class="demo-content">
              <div class="nav-item active">
                <ava-icon iconName="Home"></ava-icon>
                <span>Overview</span>
              </div>
              <div class="nav-item">
                <ava-icon iconName="ChartBar"></ava-icon>
                <span>Reports</span>
              </div>
              <div class="nav-item">
                <ava-icon iconName="Users"></ava-icon>
                <span>Customers</span>
              </div>
              <div class="nav-item">
                <ava-icon iconName="ShoppingCart"></ava-icon>
                <span>Orders</span>
              </div>
            </div>
            <div slot="footer" class="demo-footer-content">
              <div class="user-info">
                <ava-icon iconName="User"></ava-icon>
                <span>Admin User</span>
              </div>
            </div>
          </ava-sidebar>
        </div>
      </div>
    </div>
  </section>

  <!-- API Reference Section -->
  <section class="api-section">
    <div class="section-header">
      <h2>API Reference</h2>
      <p>Complete reference for all sidebar component properties and events</p>
    </div>

    <div class="api-content">
      <div class="api-table">
        <h3>Properties</h3>
        <table>
          <thead>
            <tr>
              <th>Property</th>
              <th>Type</th>
              <th>Default</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code>width</code></td>
              <td>string</td>
              <td>'260px'</td>
              <td>Width of the expanded sidebar</td>
            </tr>
            <tr>
              <td><code>collapsedWidth</code></td>
              <td>string</td>
              <td>'50px'</td>
              <td>Width of the collapsed sidebar</td>
            </tr>
            <tr>
              <td><code>height</code></td>
              <td>string</td>
              <td>'100vh'</td>
              <td>Height of the sidebar</td>
            </tr>
            <tr>
              <td><code>position</code></td>
              <td>'left' | 'right'</td>
              <td>'left'</td>
              <td>Position of the sidebar</td>
            </tr>
            <tr>
              <td><code>showCollapseButton</code></td>
              <td>boolean</td>
              <td>false</td>
              <td>Whether to show the collapse/expand button</td>
            </tr>
            <tr>
              <td><code>buttonVariant</code></td>
              <td>'inside' | 'outside'</td>
              <td>'inside'</td>
              <td>Position of the collapse button</td>
            </tr>
            <tr>
              <td><code>showHeader</code></td>
              <td>boolean</td>
              <td>true</td>
              <td>Whether to show the header section</td>
            </tr>
            <tr>
              <td><code>showFooter</code></td>
              <td>boolean</td>
              <td>true</td>
              <td>Whether to show the footer section</td>
            </tr>
            <tr>
              <td><code>isCollapsed</code></td>
              <td>boolean</td>
              <td>false</td>
              <td>Initial collapsed state</td>
            </tr>
            <tr>
              <td><code>hoverAreaWidth</code></td>
              <td>string</td>
              <td>'10px'</td>
              <td>Width of the hover area for outside button variant</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="api-table">
        <h3>Events</h3>
        <table>
          <thead>
            <tr>
              <th>Event</th>
              <th>Type</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code>collapseToggle</code></td>
              <td>EventEmitter&lt;boolean&gt;</td>
              <td>Emitted when the sidebar is collapsed or expanded</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="api-table">
        <h3>Content Slots</h3>
        <table>
          <thead>
            <tr>
              <th>Slot</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code>slot="header"</code></td>
              <td>Content for the header section</td>
            </tr>
            <tr>
              <td><code>slot="content"</code></td>
              <td>Main content area of the sidebar</td>
            </tr>
            <tr>
              <td><code>slot="footer"</code></td>
              <td>Content for the footer section</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </section>
</div>
