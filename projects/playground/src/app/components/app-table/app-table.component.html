<!-- 

<div class="table-container">
  <h2>Sample Data</h2>
  <ava-table
    [columns]="table1Columns"
    [data]="table1Data">
  </ava-table>
</div>


<div class="table-container">
<h2>Product Data</h2>
  <ava-table
    [columns]="table2Columns"
    [data]="table2Data">
  </ava-table>
  </div>


<div class="table-container">
  <h2>Order Data</h2>
  <ava-table
    [columns]="orderColumns"
    [data]="orderData">
  </ava-table>
</div>
 -->

<!-- <div class="documentation container"> -->
<!-- Header -->
<div class="row">
  <div class="col-12">
    <header class="doc-header">
      <h1>Table Component</h1>
      <p class="description">
        A versatile table component that displays data in a structured format
        with sortable columns and customizable content. Built with accessibility
        and user experience in mind.
      </p>
    </header>
  </div>
</div>

<!-- Installation -->
<div class="row">
  <div class="col-12">
    <section class="doc-section">
      <h2>Installation</h2>
      <div class="code-block">
        <pre><code>import {{ '{' }} TableComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
      </div>
    </section>
  </div>
</div>

<!-- Documentation Sections -->
<div class="doc-sections">
  <section *ngFor="let section of sections; let i = index" class="doc-section">
    <div class="row">
      <div class="col-12">
        <div class="section-header" tabindex="0" role="button">
          <h2>{{ section.title }}</h2>
          <div class="description-container">
            <p>{{ section.description }}</p>
            <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
              <span *ngIf="!section.showCode">View Code</span>
              <span *ngIf="section.showCode">Hide Code</span>
              <!-- <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'" iconColor="action"></awe-icons> -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Code Example -->
    <div class="code-example" [class.expanded]="section.showCode">
      <div class="example-preview">
        <ng-container [ngSwitch]="section.title">
          <div class="table-container">
            <h2>Sample Data</h2>
            <ava-table [columns]="table1Columns" [data]="table1Data">
            </ava-table>
          </div>
          <div class="table-container">
            <h2>Product Data</h2>
            <ava-table [columns]="table2Columns" [data]="table2Data">
            </ava-table>
          </div>
          <div class="table-container">
            <h2>Order Data</h2>
            <ava-table [columns]="orderColumns" [data]="orderData"> </ava-table>
          </div>

          <div class="table-container">
            <h2>User Table</h2>
            <ava-skeleton
              *ngIf="isLoading"
              [rows]="pageSize"
              skeletonType="table"
              [columns]="columnData.length"
            ></ava-skeleton>
            <aava-user-table
              *ngIf="!isLoading"
              [columns]="columnData"
              [data]="rowData"
              [sortAscIcon]="'move-up'"
              [sortDescIcon]="'move-down'"
              [useDefaultFilter]="true"
              [useCustomFilter]="false"
              [showCheckbox]="true"
              [filterIcon]="'list-filter'"
              (cellClick)="onTableCellClick($event)"
              (actionTriggered)="onActionFromTable($event)"
              (columnOrderChanged)="handleColumnOrderChange($event)"
              (rowOrderChanged)="handleRowOrderChange($event)"
              (selectedRowsChange)="onSelectedRowsChanged($event)"
            >
            </aava-user-table>
          </div>
        </ng-container>
      </div>
    </div>
  </section>
</div>

<!-- API Reference -->
<div class="row">
  <div class="col-12">
    <section class="doc-section api-reference">
      <h2>API Reference</h2>
      <table class="api-table">
        <thead>
          <tr>
            <th>Property</th>
            <th>Type</th>
            <th>Default</th>
            <th>Description</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let prop of apiProps">
            <td>
              <code>{{ prop.name }}</code>
            </td>
            <td>
              <code>{{ prop.type }}</code>
            </td>
            <td>
              <code>{{ prop.default }}</code>
            </td>
            <td>{{ prop.description }}</td>
          </tr>
        </tbody>
      </table>
    </section>
  </div>
</div>
