import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  TableColumn,
  TableComponent,
} from '../../../../../play-comp-library/src/lib/components/table/table.component';
import { AavaUserTableComponent } from '../../../../../play-comp-library/src/lib/components/user-table/aava-user-table.component';
import { SkeletonComponent } from '../../../../../play-comp-library/src/lib/components/skeleton/skeleton.component';

interface TableDocSection {
  title: string;
  description: string;
  showCode: boolean;
}

interface ApiProperty {
  name: string;
  type: string;
  default: string;
  description: string;
}

export interface TagData {
  label: string;
  color?:
  | 'default'
  | 'primary'
  | 'success'
  | 'warning'
  | 'error'
  | 'info'
  | 'custom';
  customStyle?: Record<string, string>;
  variant?: 'filled' | 'outlined';
  removable?: boolean;
  disabled?: boolean;
  icon?: string;
  iconPosition?: 'start' | 'end';
  avatar?: string;
  pill?: boolean;
  size?: 'small' | 'medium' | 'large';
  customClass?: string;
  iconColor?: string;
}
export interface FieldWithIcon {
  value: string;
  iconName: string;
  clickable: boolean;
}

export interface ActionConfig {
  enabled: boolean;
  label: string;
  icon: string;
  inline: boolean;
}

export interface IconRowData {
  id?: string;
  parentId?: string | null;
  name: FieldWithIcon;
  email: FieldWithIcon;
  access: FieldWithIcon;
  addedOn?: FieldWithIcon;
  validtill?: FieldWithIcon;
  lastLogin?: FieldWithIcon;
  authorized: FieldWithIcon;
  status?: TagData[];
  action: Record<string, ActionConfig>;
  sortOrder: number;
  isSelected?: boolean;
}

// For convenience
export type User = IconRowData;
export type RowDataKey = keyof IconRowData;

// Used for defining column display settings
export interface ColumnConfig {
  field: RowDataKey | 'actions' | 'select' | 'status'; // 'actions' is handled separately in UI
  label: string;
  sortable: boolean;
  filterable: boolean;
  sortingOrder?: number;
  visible: boolean;
  resizable?: boolean;
}

@Component({
  selector: 'app-table-documentation',
  imports: [
    CommonModule,
    FormsModule,
    TableComponent,
    AavaUserTableComponent,
    SkeletonComponent,
  ],
  templateUrl: './app-table.component.html',
  styleUrls: ['./app-table.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class AppTableComponent {
  isLoading = true;
  pageSize = 6;
  rowData: User[] = [];
  ngOnInit() {
    setTimeout(() => {
      this.rowtableData();
      this.isLoading = false;
    }, 2000);
  }
  sections: TableDocSection[] = [
    {
      title: 'Basic Table',
      description: 'A simple table with text columns.',
      showCode: false,
    },
  ];

  apiProps: ApiProperty[] = [
    {
      name: 'columns',
      type: 'TableColumn[]',
      default: '[]',
      description: 'Defines the columns of the table.',
    },
    {
      name: 'data',
      type: 'any[]',
      default: '[]',
      description: 'The data to display in the table.',
    },
    {
      name: 'rowActions',
      type: 'string[]',
      default: '[]',
      description: 'Actions available for each row.',
    },
    {
      name: 'statusTags',
      type: 'any[]',
      default: '[]',
      description: 'Tags to display status in a column.',
    },
  ];

  table1Columns: TableColumn[] = [
    {
      key: 'modal',
      label: 'Model',
      type: 'text',
      bgColor: '#f0f0f0',
      textColor: '#333',
    },
    {
      key: 'latency',
      label: 'Latency',
      type: 'text',
      bgColor: '#f0f0f0',
      textColor: '#333',
    },
    {
      key: 'status',
      label: 'Status',
      type: 'status',
      bgColor: '#f0f0f0',
      textColor: '#333',
    },
    {
      key: 'action',
      label: 'Actions',
      type: 'actions',
      bgColor: '#f0f0f0',
      textColor: '#333',
    },
  ];

  table1Data = [
    {
      modal: 'Model A',
      latency: '100ms',
      tags: [{ label: 'Active', color: 'primary', variant: 'filled' }],
      actions: [
        { icon: 'check', iconColor: 'blue' },
        { icon: 'x', iconColor: 'red' },
        { icon: 'trash', iconColor: 'green' },
        { icon: 'settings', iconColor: 'orange' },
      ],
    },
    {
      modal: 'Model B',
      latency: '150ms',
      tags: [{ label: 'Inactive', color: 'warning', variant: 'filled' }],
      actions: [
        { icon: 'check', iconColor: 'blue' },
        { icon: 'x', iconColor: 'red' },
        { icon: 'trash', iconColor: 'green' },
        { icon: 'settings', iconColor: 'orange' },
      ],
    },
    {
      modal: 'Model C',
      latency: '200ms',
      tags: [{ label: 'Active', color: 'primary', variant: 'filled' }],
      actions: [
        { icon: 'check', iconColor: 'blue' },
        { icon: 'x', iconColor: 'red' },
        { icon: 'trash', iconColor: 'green' },
        { icon: 'settings', iconColor: 'orange' },
      ],
    },
    {
      modal: 'Model D',
      latency: '250ms',
      tags: [{ label: 'Error', color: 'error', variant: 'filled' }],
      actions: [
        { icon: 'check', iconColor: 'blue' },
        { icon: 'x', iconColor: 'red' },
        { icon: 'trash', iconColor: 'green' },
        { icon: 'settings', iconColor: 'orange' },
      ],
    },
    {
      modal: 'Model E',
      latency: '300ms',
      tags: [{ label: 'Completed', color: 'success', variant: 'filled' }],
      actions: [
        { icon: 'check', iconColor: 'blue' },
        { icon: 'x', iconColor: 'red' },
        { icon: 'trash', iconColor: 'green' },
        { icon: 'settings', iconColor: 'orange' },
      ],
    },
    {
      modal: 'Model F',
      latency: '350ms',
      tags: [{ label: 'Inactive', color: 'warning', variant: 'filled' }],
      actions: [
        { icon: 'check', iconColor: 'blue' },
        { icon: 'x', iconColor: 'red' },
        { icon: 'trash', iconColor: 'green' },
        { icon: 'settings', iconColor: 'orange' },
      ],
    },
    {
      modal: 'Model G',
      latency: '400ms',
      tags: [{ label: 'Error', color: 'error', variant: 'filled' }],
      actions: [
        { icon: 'check', iconColor: 'blue' },
        { icon: 'x', iconColor: 'red' },
        { icon: 'trash', iconColor: 'green' },
        { icon: 'settings', iconColor: 'orange' },
      ],
    },
    {
      modal: 'Model H',
      latency: '450ms',
      tags: [{ label: 'Active', color: 'primary', variant: 'filled' }],
      actions: [
        { icon: 'check', iconColor: 'blue' },
        { icon: 'x', iconColor: 'red' },
        { icon: 'trash', iconColor: 'green' },
        { icon: 'settings', iconColor: 'orange' },
      ],
    },
  ];

  table2Columns: TableColumn[] = [
    { key: 'productId', label: 'Product ID', type: 'text' },
    { key: 'productName', label: 'Product Name', type: 'text' },
    { key: 'price', label: 'Price', type: 'text' },
    { key: 'stock', label: 'Stock', type: 'text' },
  ];

  table2Data = [
    {
      productId: 'P001',
      productName: 'Laptop',
      price: '$999',
      stock: 'In Stock',
    },
    {
      productId: 'P002',
      productName: 'Smartphone',
      price: '$699',
      stock: 'Out of Stock',
    },
    {
      productId: 'P003',
      productName: 'phone',
      price: '$399',
      stock: 'Stock',
    },
  ];

  orderColumns = [
    { key: 'orderId', label: 'Order ID', type: 'text' },
    { key: 'customer', label: 'Customer', type: 'text' },
    { key: 'amount', label: 'Amount', type: 'text' },
    { key: 'status', label: 'Status', type: 'status' },
  ];

  orderData = [
    {
      orderId: 'A001',
      customer: 'Alice',
      amount: '$120.00',
      tags: [{ label: 'Completed', color: 'success', variant: 'filled' }],
    },
    {
      orderId: 'A002',
      customer: 'Bob',
      amount: '$80.00',
      tags: [{ label: 'Pending', color: 'warning', variant: 'filled' }],
    },
    {
      orderId: 'A003',
      customer: 'phone',
      amount: '$50.00',
      tags: [{ label: 'Active', color: 'primary', variant: 'filled' }],
    },
  ];

  columnData: ColumnConfig[] = [
    {
      field: 'select',
      label: '',
      sortable: false,
      filterable: false,
      visible: true,
      resizable: false,
    },
    {
      field: 'name',
      label: 'Name',
      sortable: true,
      filterable: true,
      sortingOrder: 1,
      visible: true,
    },
    {
      field: 'email',
      label: 'Email',
      sortable: true,
      filterable: true,
      sortingOrder: 2,
      visible: true,
    },
    {
      field: 'access',
      label: 'Access',
      sortable: true,
      filterable: true,
      sortingOrder: 3,
      visible: true,
    },
    {
      field: 'addedOn',
      label: 'Added On',
      sortable: false,
      filterable: false,
      sortingOrder: 4,
      visible: true,
    },
    {
      field: 'validtill',
      label: 'Valid Till',
      sortable: true,
      filterable: true,
      sortingOrder: 5,
      visible: true,
    },
    {
      field: 'lastLogin',
      label: 'Last Login',
      sortable: true,
      filterable: false,
      sortingOrder: 6,
      visible: true,
    },
    {
      field: 'authorized',
      label: 'Authorized by',
      sortable: true,
      filterable: false,
      sortingOrder: 7,
      visible: true,
    },
    {
      field: 'status',
      label: 'Status',
      sortable: false,
      filterable: false,
      sortingOrder: 8,
      visible: true,
    },
    {
      field: 'actions',
      label: 'Actions',
      sortable: false,
      filterable: false,
      sortingOrder: 9,
      visible: true,
    },
  ];
  rowtableData() {
    this.rowData = [
      {
        id: '1',
        parentId: '1',
        name: { value: 'Elias Montgomery', iconName: '', clickable: false },
        email: { value: '<EMAIL>', iconName: '', clickable: true },
        access: { value: 'Admin', iconName: '', clickable: false },
        addedOn: { value: '11/02/2023', iconName: '', clickable: false },
        validtill: { value: '12/02/2023', iconName: '', clickable: false },
        lastLogin: { value: '10/02/2023', iconName: '', clickable: false },
        status: [
          {
            label: 'Posted',
            color: 'custom',
            variant: 'filled',
            customStyle: {
              background: '#e6f5f0',
              color: '#36ab87',
              'font-weight': '600',
              'border-radius': '2px',
            },
          },
        ],
        authorized: {
          value: '<EMAIL>',
          iconName: '',
          clickable: false,
        },
        action: {
          edit: {
            enabled: true,
            label: 'Edit',
            icon: 'SquarePen',
            inline: true,
          },
          delete: {
            enabled: true,
            label: 'Delete',
            icon: 'trash',
            inline: true,
          },
        },
        sortOrder: 1,
        isSelected: false,
      },
      {
        id: '2',
        parentId: '2',
        name: {
          value: 'Clara Montgomery',
          iconName: '',
          clickable: false,
        },
        email: { value: '<EMAIL>', iconName: '', clickable: true },
        access: { value: 'lead', iconName: '', clickable: false },
        addedOn: { value: '12/02/2023', iconName: '', clickable: false },
        validtill: { value: '12/02/2023', iconName: '', clickable: false },
        lastLogin: { value: '12/02/2023', iconName: '', clickable: false },
        authorized: {
          value: '<EMAIL>',
          iconName: '',
          clickable: false,
        },
        status: [
          {
            label: 'Rejected',
            color: 'custom',
            variant: 'filled',
            customStyle: {
              background: '#fbecec',
              color: '#e14444',
              'font-weight': '600',
              'padding-bottom': '2px',
              'border-radius': '2px'
            },
          },
        ],
        action: {
          edit: {
            enabled: true,
            label: 'Edit',
            icon: 'SquarePen',
            inline: false,
          },
          delete: {
            enabled: true,
            label: 'Delete',
            icon: 'trash',
            inline: false,
          },
        },
        sortOrder: 2,
        isSelected: false,
      },
      {
        id: '3',
        parentId: '3',
        name: { value: 'Alias', iconName: '', clickable: false },
        email: { value: '<EMAIL>', iconName: '', clickable: true },
        access: { value: 'Admin', iconName: '', clickable: false },
        addedOn: { value: '11/02/2023', iconName: '', clickable: false },
        validtill: { value: '12/02/2023', iconName: '', clickable: false },
        lastLogin: { value: '10/02/2023', iconName: '', clickable: false },
        authorized: {
          value: '<EMAIL>',
          iconName: '',
          clickable: false,
        },
        status: [
          {
            label: 'Templete',
            color: 'custom',
            variant: 'filled',
            customStyle: {
              background: '#fbf1e6',
              color: '#d08530',
              'font-weight': '600',
              'padding-bottom': '2px',
              'border-radius': '2px'
            },
          },
        ],
        action: {
          edit: {
            enabled: true,
            label: 'Edit',
            icon: 'SquarePen',
            inline: true,
          },
          delete: {
            enabled: true,
            label: 'Delete',
            icon: 'trash',
            inline: true,
          },
        },
        sortOrder: 3,
        isSelected: false,
      },
      {
        id: '4',
        parentId: '4',
        name: { value: 'Montgomery', iconName: '', clickable: false },
        email: { value: '<EMAIL>', iconName: '', clickable: true },
        access: { value: 'Admin', iconName: '', clickable: false },
        addedOn: { value: '11/02/2023', iconName: '', clickable: false },
        validtill: { value: '12/02/2023', iconName: '', clickable: false },
        lastLogin: { value: '10/02/2023', iconName: '', clickable: false },
        authorized: {
          value: '<EMAIL>',
          iconName: '',
          clickable: false,
        },
        status: [
          {
            label: 'Ready to Approve',
            color: 'custom',
            variant: 'filled',
            customStyle: {
              background: '#e6f4f7',
              color: '#3ba8c2',
              'font-weight': '600',
              'padding-bottom': '2px',
              'border-radius': '2px'
            },
          },
        ],
        action: {
          edit: {
            enabled: true,
            label: 'Edit',
            icon: 'SquarePen',
            inline: true,
          },
          delete: {
            enabled: true,
            label: 'Delete',
            icon: 'trash',
            inline: true,
          },
        },
        sortOrder: 4,
        isSelected: false,
      },
      {
        id: '5',
        parentId: '5',
        name: { value: 'lias Montgomer', iconName: '', clickable: false },
        email: { value: '<EMAIL>', iconName: '', clickable: true },
        access: { value: 'Admin1', iconName: '', clickable: false },
        addedOn: { value: '11/02/2023', iconName: '', clickable: false },
        validtill: { value: '12/02/2023', iconName: '', clickable: false },
        lastLogin: { value: '10/02/2023', iconName: '', clickable: false },
        authorized: {
          value: '<EMAIL>',
          iconName: '',
          clickable: false,
        },
        status: [
          {
            label: 'Posted',
            color: 'custom',
            variant: 'filled',
            customStyle: {
              background: '#e6f5f0',
              color: '#36ab87',
              'font-weight': '600',
              'padding-bottom': '2px',
              'border-radius': '2px'
            },
          },
        ],
        action: {
          edit: {
            enabled: true,
            label: 'Edit',
            icon: 'SquarePen',
            inline: true,
          },
          delete: {
            enabled: true,
            label: 'Delete',
            icon: 'trash',
            inline: true,
          },
        },
        sortOrder: 5,
        isSelected: false,
      },
    ];
  }

  onTableCellClick(event: { row: User; field: string }) {
    console.log('Clicked cell data:', event);
  }

  onActionFromTable(event: {
    row: User;
    actionKey: string;
    config: ActionConfig;
  }): void {
    console.log('Action clicked:', event.actionKey);
    console.log('Label:', event.config.label);
    console.log('Row:', event.row);

    if (event.actionKey === 'edit') {
      // handle edit
    } else if (event.actionKey === 'delete') {
      // handle delete
    }
  }

  handleColumnOrderChange(updatedColumns: ColumnConfig[]) {
    console.log('Updated column order:', updatedColumns);
  }

  handleRowOrderChange(updatedData: User[]) {
    console.log('Updated row order:', updatedData);
  }

  onSelectedRowsChanged(selectedRows: any[]): void {
    console.log('Selected rows:', selectedRows);
  }

  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation();
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  getTableCode(sectionTitle: string): string {
    const examples: Record<string, string> = {
      'basic table': `
<ava-table [columns]="table2Columns" [data]="table2Data"></ava-table>
`,
      'table with status': `
<ava-table [columns]="orderColumns" [data]="orderData"></ava-table>
`,
      'table with actions': `
<ava-table [columns]
`,
    };
    return examples[sectionTitle.toLowerCase()] || '';
  }

  copyCode(sectionTitle: string): void {
    const code = this.getTableCode(sectionTitle);
    const textarea = document.createElement('textarea');
    textarea.value = code;
    textarea.style.position = 'fixed';
    document.body.appendChild(textarea);
    textarea.select();
    try {
      document.execCommand('copy');
      console.log('Code copied to clipboard');
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
    document.body.removeChild(textarea);
  }
}
