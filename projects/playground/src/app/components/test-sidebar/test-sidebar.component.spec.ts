import { ComponentFixture, TestBed } from '@angular/core/testing';

import { TestSidebarComponent } from './test-sidebar.component';

describe('TestSidebarComponent', () => {
  let component: TestSidebarComponent;
  let fixture: ComponentFixture<TestSidebarComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TestSidebarComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(TestSidebarComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have navigation items', () => {
    expect(component.navigationItems).toBeDefined();
    expect(component.navigationItems.length).toBeGreaterThan(0);
  });

  it('should have user profile', () => {
    expect(component.userProfile).toBeDefined();
    expect(component.userProfile.name).toBe('<PERSON>');
  });

  it('should handle collapse toggle', () => {
    const initialState = component.isCollapsed;
    component.onCollapseToggle(!initialState);
    expect(component.isCollapsed).toBe(!initialState);
  });

  it('should handle navigation item click', () => {
    const testItem = component.navigationItems[1];
    component.onNavigationItemClick(testItem);
    
    expect(testItem.active).toBe(true);
    // Check that other items are not active
    const otherItems = component.navigationItems.filter(item => item.id !== testItem.id);
    otherItems.forEach(item => {
      expect(item.active).toBeFalsy();
    });
  });
});
