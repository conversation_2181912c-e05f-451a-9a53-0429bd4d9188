<div class="test-sidebar-demo">
  <h1>Reference Implementation - Sidebar Component</h1>
  <p>This demonstrates the sidebar component matching the provided reference images.</p>

  <div class="demo-container">
    <!-- Expanded Sidebar Demo -->
    <div class="demo-section">
      <h2>Expanded Sidebar (Reference Design)</h2>
      <div class="sidebar-showcase">
        <ava-sidebar
          size="medium"
          width="280px"
          collapsedWidth="70px"
          height="600px"
          [showCollapseButton]="true"
          buttonVariant="inside"
          [showHeader]="true"
          [showFooter]="true"
          position="left"
          [isCollapsed]="false"
          (collapseToggle)="onCollapseToggle($event)"
        >
          <!-- Header with Logo -->
          <div slot="header" class="sidebar-header-content">
            <div class="logo-section">
              <div class="logo-container">
                <span class="logo-text">ASCENDION</span>
              </div>
            </div>
          </div>

          <!-- Main Navigation Content -->
          <div slot="content" class="sidebar-main-content">
            <!-- Search Bar -->
            <div class="search-section">
              <div class="search-container">
                <ava-icon iconName="Search" iconSize="16" iconColor="#9CA3AF"></ava-icon>
                <input type="text" placeholder="Search" class="search-input">
              </div>
            </div>

            <!-- Navigation Items -->
            <div class="navigation-section">
              <div 
                *ngFor="let item of navigationItems" 
                class="nav-item"
                [class.active]="item.active"
                (click)="onNavigationItemClick(item)"
              >
                <div class="nav-item-icon">
                  <ava-icon 
                    [iconName]="item.icon" 
                    iconSize="20" 
                    [iconColor]="item.active ? '#FFFFFF' : '#6B7280'"
                  ></ava-icon>
                </div>
                <span class="nav-item-text">{{ item.label }}</span>
              </div>
            </div>
          </div>

          <!-- Footer with User Profile -->
          <div slot="footer" class="sidebar-footer-content">
            <div class="user-profile" (click)="onUserProfileClick()">
              <div class="user-avatar">
                <img src="/assets/images/user-avatar.jpg" alt="User Avatar" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <div class="avatar-fallback">
                  <ava-icon iconName="User" iconSize="24" iconColor="#6B7280"></ava-icon>
                </div>
              </div>
              <div class="user-info">
                <div class="user-name">{{ userProfile.name }}</div>
                <div class="user-role">View Profile</div>
              </div>
            </div>
          </div>
        </ava-sidebar>
      </div>
    </div>

    <!-- Collapsed Sidebar Demo -->
    <div class="demo-section">
      <h2>Collapsed Sidebar (Reference Design)</h2>
      <div class="sidebar-showcase">
        <ava-sidebar
          size="medium"
          width="280px"
          collapsedWidth="70px"
          height="600px"
          [showCollapseButton]="true"
          buttonVariant="inside"
          [showHeader]="true"
          [showFooter]="true"
          position="left"
          [isCollapsed]="true"
          (collapseToggle)="onCollapseToggle($event)"
        >
          <!-- Header with Logo (Collapsed) -->
          <div slot="header" class="sidebar-header-content">
            <div class="logo-section">
              <div class="logo-container">
                <span class="logo-text">A</span>
              </div>
            </div>
          </div>

          <!-- Main Navigation Content (Collapsed) -->
          <div slot="content" class="sidebar-main-content">
            <!-- Search Icon Only -->
            <div class="search-section">
              <div class="search-icon-only">
                <ava-icon iconName="Search" iconSize="20" iconColor="#6B7280"></ava-icon>
              </div>
            </div>

            <!-- Navigation Icons Only -->
            <div class="navigation-section">
              <div 
                *ngFor="let item of navigationItems" 
                class="nav-item"
                [class.active]="item.active"
                (click)="onNavigationItemClick(item)"
              >
                <div class="nav-item-icon">
                  <ava-icon 
                    [iconName]="item.icon" 
                    iconSize="20" 
                    [iconColor]="item.active ? '#FFFFFF' : '#6B7280'"
                  ></ava-icon>
                </div>
              </div>
            </div>
          </div>

          <!-- Footer with User Avatar Only -->
          <div slot="footer" class="sidebar-footer-content">
            <div class="user-profile" (click)="onUserProfileClick()">
              <div class="user-avatar">
                <img src="/assets/images/user-avatar.jpg" alt="User Avatar" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <div class="avatar-fallback">
                  <ava-icon iconName="User" iconSize="24" iconColor="#6B7280"></ava-icon>
                </div>
              </div>
            </div>
          </div>
        </ava-sidebar>
      </div>
    </div>

    <!-- Interactive Demo -->
    <div class="demo-section">
      <h2>Interactive Demo</h2>
      <div class="sidebar-showcase">
        <ava-sidebar
          size="medium"
          width="280px"
          collapsedWidth="70px"
          height="600px"
          [showCollapseButton]="true"
          buttonVariant="inside"
          [showHeader]="true"
          [showFooter]="true"
          position="left"
          [isCollapsed]="isCollapsed"
          (collapseToggle)="onCollapseToggle($event)"
        >
          <!-- Header with Logo -->
          <div slot="header" class="sidebar-header-content">
            <div class="logo-section">
              <div class="logo-container">
                <span class="logo-text">{{ isCollapsed ? 'A' : 'ASCENDION' }}</span>
              </div>
            </div>
          </div>

          <!-- Main Navigation Content -->
          <div slot="content" class="sidebar-main-content">
            <!-- Search Bar -->
            <div class="search-section" *ngIf="!isCollapsed">
              <div class="search-container">
                <ava-icon iconName="Search" iconSize="16" iconColor="#9CA3AF"></ava-icon>
                <input type="text" placeholder="Search" class="search-input">
              </div>
            </div>

            <!-- Search Icon Only -->
            <div class="search-section" *ngIf="isCollapsed">
              <div class="search-icon-only">
                <ava-icon iconName="Search" iconSize="20" iconColor="#6B7280"></ava-icon>
              </div>
            </div>

            <!-- Navigation Items -->
            <div class="navigation-section">
              <div 
                *ngFor="let item of navigationItems" 
                class="nav-item"
                [class.active]="item.active"
                (click)="onNavigationItemClick(item)"
              >
                <div class="nav-item-icon">
                  <ava-icon 
                    [iconName]="item.icon" 
                    iconSize="20" 
                    [iconColor]="item.active ? '#FFFFFF' : '#6B7280'"
                  ></ava-icon>
                </div>
                <span class="nav-item-text" *ngIf="!isCollapsed">{{ item.label }}</span>
              </div>
            </div>
          </div>

          <!-- Footer with User Profile -->
          <div slot="footer" class="sidebar-footer-content">
            <div class="user-profile" (click)="onUserProfileClick()">
              <div class="user-avatar">
                <img src="/assets/images/user-avatar.jpg" alt="User Avatar" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <div class="avatar-fallback">
                  <ava-icon iconName="User" iconSize="24" iconColor="#6B7280"></ava-icon>
                </div>
              </div>
              <div class="user-info" *ngIf="!isCollapsed">
                <div class="user-name">{{ userProfile.name }}</div>
                <div class="user-role">View Profile</div>
              </div>
            </div>
          </div>
        </ava-sidebar>
      </div>
    </div>
  </div>
</div>
