import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SidebarComponent } from '../../../../../play-comp-library/src/lib/components/sidebar/sidebar.component';
import { IconComponent } from '../../../../../play-comp-library/src/lib/components/icon/icon.component';

interface NavigationItem {
  id: string;
  label: string;
  icon: string;
  active?: boolean;
  route?: string;
}

@Component({
  selector: 'test-sidebar',
  standalone: true,
  imports: [CommonModule, SidebarComponent, IconComponent],
  templateUrl: './test-sidebar.component.html',
  styleUrl: './test-sidebar.component.scss',
})
export class TestSidebarComponent {
  isCollapsed = false;

  navigationItems: NavigationItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: 'LayoutDashboard',
      active: true,
      route: '/dashboard'
    },
    {
      id: 'requirement-analyser',
      label: 'Requirement Analyser',
      icon: 'TrendingUp',
      route: '/requirement-analyser'
    },
    {
      id: 'test-case-scenario',
      label: 'Test Case Scenario',
      icon: 'Shield',
      route: '/test-case-scenario'
    },
    {
      id: 'test-case-creation',
      label: 'Test Case Creation',
      icon: 'Plus',
      route: '/test-case-creation'
    },
    {
      id: 'automation-script',
      label: 'Automation Script Creation',
      icon: 'FileText',
      route: '/automation-script'
    },
    {
      id: 'conversion',
      label: 'Conversion',
      icon: 'RefreshCw',
      route: '/conversion'
    },
    {
      id: 'test-optimization',
      label: 'Test Optimization',
      icon: 'Settings',
      route: '/test-optimization'
    },
    {
      id: 'data-management',
      label: 'Data Management',
      icon: 'Database',
      route: '/data-management'
    },
    {
      id: 'request-history',
      label: 'Request History',
      icon: 'Clock',
      route: '/request-history'
    },
    {
      id: 'ask-me',
      label: 'Ask Me',
      icon: 'MessageCircle',
      route: '/ask-me'
    }
  ];

  userProfile = {
    name: 'John Doe',
    avatar: '/assets/images/user-avatar.jpg',
    role: 'Developer'
  };

  onCollapseToggle(collapsed: boolean): void {
    this.isCollapsed = collapsed;
  }

  onNavigationItemClick(item: NavigationItem): void {
    // Update active state
    this.navigationItems.forEach(navItem => {
      navItem.active = navItem.id === item.id;
    });
    
    console.log('Navigation item clicked:', item);
    // Here you would typically handle routing
  }

  onUserProfileClick(): void {
    console.log('User profile clicked');
    // Handle user profile actions
  }
}
