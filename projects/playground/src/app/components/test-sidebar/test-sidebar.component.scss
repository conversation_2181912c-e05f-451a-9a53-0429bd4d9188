.test-sidebar-demo {
  padding: 2rem;
  background: #f8fafc;
  min-height: 100vh;

  h1 {
    color: #1f2937;
    margin-bottom: 0.5rem;
    font-size: 2rem;
    font-weight: 700;
  }

  p {
    color: #6b7280;
    margin-bottom: 2rem;
    font-size: 1.1rem;
  }

  h2 {
    color: #374151;
    margin-bottom: 1rem;
    font-size: 1.5rem;
    font-weight: 600;
  }
}

.demo-container {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.demo-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.sidebar-showcase {
  display: flex;
  justify-content: flex-start;
  padding: 1rem;
  background: #f1f5f9;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

// Sidebar Header Styles
.sidebar-header-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.logo-section {
  flex: 1;
}

.logo-container {
  background: #000000;
  color: white;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  text-align: center;
  font-weight: 700;
  font-size: 0.875rem;
  letter-spacing: 0.05em;
}

// Sidebar Main Content Styles
.sidebar-main-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: 100%;
}

// Search Section Styles
.search-section {
  margin-bottom: 1rem;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.2s ease;

  &:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  outline: none;
  font-size: 0.875rem;
  color: #374151;

  &::placeholder {
    color: #9ca3af;
  }
}

.search-icon-only {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin: 0 auto;
}

// Navigation Section Styles
.navigation-section {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;

  &:hover {
    background: #f1f5f9;
    color: #374151;
  }

  &.active {
    background: #e11d48;
    color: white;

    .nav-item-icon ava-icon {
      color: white;
    }
  }
}

.nav-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.nav-item-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// Sidebar Footer Styles
.sidebar-footer-content {
  border-top: 1px solid #e5e7eb;
  padding-top: 1rem;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #f1f5f9;
  }
}

.user-avatar {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  background: #f3f4f6;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .avatar-fallback {
    display: none;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: #e5e7eb;
  }
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-role {
  font-size: 0.75rem;
  color: #e11d48;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// Collapsed state adjustments
.ava-sidebar.collapsed {
  .logo-container {
    padding: 0.75rem 0.5rem;
    font-size: 1rem;
  }

  .nav-item {
    justify-content: center;
    padding: 0.75rem;
    width: 40px;
    height: 40px;
    margin: 0.25rem auto;
  }

  .user-profile {
    justify-content: center;
    padding: 0.75rem;
  }

  .search-icon-only {
    width: 40px;
    height: 40px;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .demo-container {
    gap: 2rem;
  }

  .demo-section {
    padding: 1.5rem;
  }

  .sidebar-showcase {
    padding: 0.5rem;
  }
}
