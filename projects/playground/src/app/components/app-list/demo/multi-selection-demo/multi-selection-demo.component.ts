import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ListComponent,
  ListItem,
  ListSelectionEvent,
} from '../../../../../../../play-comp-library/src/lib/components/list/list.component';

@Component({
  selector: 'ava-list-multi-selection-demo',
  standalone: true,
  imports: [CommonModule, ListComponent],
  template: `
    <div class="demo-container">
      <div class="demo-section">
        <ava-list
          [items]="richItems"
          [title]="'Select Users'"
          [height]="'400px'"
          [width]="'500px'"
          [multiSelect]="true"
          [showCheckboxes]="true"
          [selectedItemIds]="selectedRichIds"
          (onSelectionChanged)="onRichSelectionChanged($event)"
        ></ava-list>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        display: flex;
        justify-content: center;
        min-height: 100vh;
        margin-top: 3rem;
        padding-top: 0;
      }

      .demo-description {
        color: #666;
        margin-bottom: 30px;
        font-size: 16px;
      }

      .demo-section {
        margin-bottom: 40px;
      }

      .demo-section h3 {
        color: #333;
        margin-bottom: 15px;
        font-size: 18px;
      }

      .demo-info {
        margin-top: 15px;
        padding: 10px;
        background-color: #f8f9fa;
        border-radius: 6px;
        border-left: 4px solid #007bff;
      }

      .demo-info p {
        margin: 0 0 5px 0;
        color: #495057;
      }

      .demo-info p:last-child {
        margin-bottom: 0;
      }

      .demo-controls {
        margin-top: 30px;
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
      }

      .control-btn {
        padding: 8px 16px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
      }

      .control-btn:hover {
        background-color: #0056b3;
      }

      .control-btn:active {
        background-color: #004085;
      }
    `,
  ],
})
export class MultiSelectionDemoComponent {
  // Checkbox multi-select
  selectedCheckboxIds: string[] = [];
  selectedCheckboxItems: ListItem[] = [];
  checkboxItems: ListItem[] = [
    { id: '1', title: 'John Doe', subtitle: 'Software Engineer' },
    { id: '2', title: 'Jane Smith', subtitle: 'Product Manager' },
    { id: '3', title: 'Bob Johnson', subtitle: 'UX Designer' },
    { id: '4', title: 'Alice Brown', subtitle: 'Data Scientist' },
    { id: '5', title: 'Charlie Wilson', subtitle: 'DevOps Engineer' },
  ];

  // Click multi-select
  selectedClickIds: string[] = [];
  selectedClickItems: ListItem[] = [];
  clickItems: ListItem[] = [
    { id: '1', title: 'Product A', subtitle: '$99.99' },
    { id: '2', title: 'Product B', subtitle: '$149.99' },
    { id: '3', title: 'Product C', subtitle: '$199.99' },
    { id: '4', title: 'Product D', subtitle: '$299.99' },
    { id: '5', title: 'Product E', subtitle: '$399.99' },
    { id: '6', title: 'Product F', subtitle: '$499.99' },
  ];

  // Rich multi-select
  selectedRichIds: string[] = [];
  selectedRichItems: ListItem[] = [];
  richItems: ListItem[] = [
    {
      id: '1',
      title: 'Mariana',
      subtitle: 'Software Engineer',
      avatar: {
        profileText: 'M',
        size: 'medium',
        shape: 'pill',
        active: true,
        imageUrl: 'assets/1.png',
        badgeSize: 'sm',
      },
      icon: {
        iconName: 'User',
        iconColor: '#007bff',
        iconSize: 16,
      },
    },
    {
      id: '2',
      title: 'Kristina',
      subtitle: 'Product Manager',
      avatar: {
        profileText: 'K',
        size: 'medium',
        shape: 'pill',
        badgeState: 'high-priority',
        badgeSize: 'sm',
        badgeCount: 2,
        imageUrl: 'assets/1.png',
      },
      icon: {
        iconName: 'Settings',
        iconColor: '#28a745',
        iconSize: 16,
      },
    },
    {
      id: '3',
      title: 'Juliana',
      subtitle: 'UX Designer',
      avatar: {
        profileText: 'J',
        size: 'medium',
        shape: 'pill',
        badgeState: 'medium-priority',
        badgeSize: 'sm',
        badgeCount: 1,
        imageUrl: 'assets/1.png',
      },
      icon: {
        iconName: 'Edit',
        iconColor: '#ffc107',
        iconSize: 16,
      },
    },
  ];

  onCheckboxSelectionChanged(event: ListSelectionEvent): void {
    this.selectedCheckboxIds = event.selectedIds;
    this.selectedCheckboxItems = event.selectedItems;
    console.log('Checkbox selection changed:', event);
  }

  onClickSelectionChanged(event: ListSelectionEvent): void {
    this.selectedClickIds = event.selectedIds;
    this.selectedClickItems = event.selectedItems;
    console.log('Click selection changed:', event);
  }

  onRichSelectionChanged(event: ListSelectionEvent): void {
    this.selectedRichIds = event.selectedIds;
    this.selectedRichItems = event.selectedItems;
    console.log('Rich selection changed:', event);
  }

  selectAll(): void {
    this.selectedCheckboxIds = this.checkboxItems.map((item) => item.id);
    this.selectedClickIds = this.clickItems.map((item) => item.id);
    this.selectedRichIds = this.richItems.map((item) => item.id);
  }

  clearSelection(): void {
    this.selectedCheckboxIds = [];
    this.selectedClickIds = [];
    this.selectedRichIds = [];
  }

  selectFirstThree(): void {
    this.selectedCheckboxIds = this.checkboxItems
      .slice(0, 3)
      .map((item) => item.id);
    this.selectedClickIds = this.clickItems.slice(0, 3).map((item) => item.id);
    this.selectedRichIds = this.richItems.slice(0, 3).map((item) => item.id);
  }

  getSelectedTitles(items: ListItem[]): string {
    return items.map((item) => item.title).join(', ');
  }
}
