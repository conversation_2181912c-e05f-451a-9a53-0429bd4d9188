import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SearchBarComponent } from '../../../../../../../play-comp-library/src/lib/composite-components/search-bar/search-bar.component';

@Component({
  selector: 'ava-search-bar-states-demo',
  standalone: true,
  imports: [CommonModule, SearchBarComponent],
  template: `
    <div class="demo-container">
      <div class="demo-section">
        <div class="state-comparison">
          <div class="state-item">
            <ava-search-bar
              id="default-compare"
              placeholder="Default"
              (searchClick)="onSearch($event, 'default-compare')"
            ></ava-search-bar>
          </div>
          <div class="state-item">
            <ava-search-bar
              id="disabled-compare"
              placeholder="Disabled"
              [disabled]="true"
              (searchClick)="onSearch($event, 'disabled-compare')"
            ></ava-search-bar>
          </div>
          <div class="state-item">
            <ava-search-bar
              id="custom-compare"
              placeholder="Custom"
              searchIconColor="#8b5cf6"
              sendIconColor="#ec4899"
              (searchClick)="onSearch($event, 'custom-compare')"
            ></ava-search-bar>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 900px;
        margin: 0 auto;
        padding: 2rem;
        margin-top: 3rem;
        text-align: center;
      }

      .demo-description {
        margin-bottom: 2rem;
      }

      .demo-description h3 {
        color: #333;
        margin-bottom: 1rem;
        font-size: 24px;
      }

      .demo-description p {
        color: #666;
        font-size: 16px;
        line-height: 1.5;
      }

      .states-demo {
        margin-bottom: 2rem;
      }

      .demo-section {
        text-align: center;
      }

      .demo-section h4 {
        color: #333;
        margin-bottom: 0.5rem;
        font-size: 20px;
        font-weight: 500;
      }

      .state-description {
        color: #666;
        font-size: 14px;
        margin-bottom: 1.5rem;
        font-style: italic;
      }

      .search-example {
        margin-bottom: 1.5rem;
        display: flex;
        justify-content: center;
      }

      .state-controls {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-bottom: 1.5rem;
      }

      .state-info {
        padding: 1rem;
        background-color: #e3f2fd;
        border-radius: 6px;
        border-left: 4px solid #2196f3;
        text-align: left;
        margin-top: 1rem;
      }

      .state-info p {
        margin: 0;
        color: #333;
        font-size: 14px;
      }

      .state-comparison {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 1.5rem;
      }

      .state-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
      }

      .state-item label {
        font-weight: 600;
        color: #333;
        font-size: 14px;
      }

      .info-section {
        padding: 1.5rem;
        background-color: #e3f2fd;
        border-radius: 8px;
        border-left: 4px solid #2196f3;
        text-align: left;
      }

      .info-section h4 {
        color: #333;
        margin-bottom: 1rem;
        font-size: 18px;
        font-weight: 500;
      }

      .feature-list {
        margin: 0;
        padding-left: 1.5rem;
        color: #666;
        line-height: 1.6;
      }

      .feature-list li {
        margin-bottom: 0.5rem;
      }

      .feature-list strong {
        color: #333;
      }
    `,
  ],
})
export class StatesDemoComponent {
  isDisabled = false;

  toggleDisabled() {
    this.isDisabled = !this.isDisabled;
  }

  onSearch(searchTerm: string, state: string) {
    console.log(`Search clicked in ${state} state:`, searchTerm);
  }

  onTextboxChange(event: Event) {
    const target = event.target as HTMLInputElement;
    console.log('Textbox changed:', target.value);
  }
}
