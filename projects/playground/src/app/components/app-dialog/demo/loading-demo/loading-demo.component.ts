import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';
import { DialogService } from '../../../../../../../play-comp-library/src/lib/components/dialog/dialog-service';

@Component({
  selector: 'app-loading-demo',
  standalone: true,
  imports: [CommonModule, ButtonComponent],
  template: `
    <div class="demo-container">
      <div class="demo-header">
        <h2>Loading Dialogs</h2>
        <p>
          Interactive loading states with progress indicators and cancellation
          options.
        </p>
      </div>

      <div class="demo-content">
        <div class="demo-section">
          <h3>Basic Loading</h3>
          <p>Simple loading dialog with spinner animation.</p>
          <ava-button
            label="Show Basic Loading"
            variant="primary"
            (userClick)="showBasicLoading()"
          ></ava-button>
        </div>

        <div class="demo-section">
          <h3>Loading with Progress</h3>
          <p>Loading dialog with progress bar for determinate operations.</p>
          <ava-button
            label="Show Loading with Progress"
            variant="success"
            (userClick)="showLoadingWithProgress()"
          ></ava-button>
        </div>

        <div class="demo-section">
          <h3>Loading with Cancel</h3>
          <p>Loading dialog with cancel button for long operations.</p>
          <ava-button
            label="Show Loading with Cancel"
            variant="warning"
            (userClick)="showLoadingWithCancel()"
          ></ava-button>
        </div>

        <div class="demo-section">
          <h3>Custom Spinner Color</h3>
          <p>Loading dialog with custom spinner color.</p>
          <ava-button
            label="Show Custom Spinner Loading"
            variant="info"
            (userClick)="showCustomSpinnerLoading()"
          ></ava-button>
        </div>

        <div class="demo-section">
          <h3>Simulated File Upload</h3>
          <p>Loading dialog simulating a file upload process.</p>
          <ava-button
            label="Show File Upload Loading"
            variant="secondary"
            (userClick)="showFileUploadLoading()"
          ></ava-button>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
      }

      .demo-header {
        text-align: center;
        margin-bottom: 3rem;
      }

      .demo-header h2 {
        color: var(--color-text-primary);
        margin-bottom: 1rem;
        font-size: 2rem;
      }

      .demo-header p {
        color: var(--color-text-secondary);
        font-size: 1.1rem;
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
      }

      .demo-content {
        display: flex;
        flex-direction: column;
        gap: 2rem;
      }

      .demo-section {
        background: var(--color-background-secondary);
        border: 1px solid var(--color-border);
        border-radius: 8px;
        padding: 1.5rem;
        text-align: center;
      }

      .demo-section h3 {
        color: var(--color-text-primary);
        margin-bottom: 0.5rem;
        font-size: 1.3rem;
      }

      .demo-section p {
        color: var(--color-text-secondary);
        margin-bottom: 1rem;
        line-height: 1.5;
      }

      ava-button {
        margin: 0.5rem;
      }
    `,
  ],
})
export class LoadingDemoComponent {
  constructor(private dialogService: DialogService) {}

  showBasicLoading() {
    this.dialogService
      .loading({
        title: 'Processing...',
        message: 'Please wait while we process your request.',
        indeterminate: true,
      })
      .then((result: unknown) => {
        console.log('Basic loading dialog closed:', result);
      });

    // Auto-close after 3 seconds
    setTimeout(() => {
      this.dialogService.close();
    }, 3000);
  }

  showLoadingWithProgress() {
    const loadingDialog = this.dialogService.loading({
      title: 'Processing...',
      message: 'Please wait while we process your request.',
      showProgress: true,
      progress: 0,
      indeterminate: false,
    });

    // Simulate progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      if (progress >= 100) {
        clearInterval(interval);
        this.dialogService.close();
      }
    }, 500);

    loadingDialog.then((result: unknown) => {
      clearInterval(interval);
      console.log('Loading with progress dialog closed:', result);
    });
  }

  showLoadingWithCancel() {
    const loadingDialog = this.dialogService.loading({
      title: 'Processing...',
      message: 'Please wait while we process your request.',
      showCancelButton: true,
      cancelButtonText: 'Cancel',
      indeterminate: true,
    });

    // Auto-close after 5 seconds
    setTimeout(() => {
      this.dialogService.close();
    }, 5000);

    loadingDialog.then((result: unknown) => {
      console.log('Loading with cancel dialog closed:', result);
      const dialogResult = result as { action?: string };
      if (dialogResult.action === 'cancel') {
        console.log('User cancelled the operation');
      }
    });
  }

  showCustomSpinnerLoading() {
    this.dialogService
      .loading({
        title: 'Custom Loading',
        message: 'Loading with custom spinner color...',
        spinnerColor: '#3b82f6',
        indeterminate: true,
      })
      .then((result: unknown) => {
        console.log('Custom spinner loading dialog closed:', result);
      });

    // Auto-close after 4 seconds
    setTimeout(() => {
      this.dialogService.close();
    }, 4000);
  }

  showFileUploadLoading() {
    const loadingDialog = this.dialogService.loading({
      title: 'Uploading File',
      message: 'Please wait while we upload your file...',
      showProgress: true,
      progress: 0,
      showCancelButton: true,
      cancelButtonText: 'Cancel Upload',
      indeterminate: false,
    });

    // Simulate file upload progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 15; // Random progress increments
      if (progress >= 100) {
        progress = 100;
        clearInterval(interval);
        this.dialogService.close();
      }
    }, 800);

    loadingDialog.then((result: unknown) => {
      clearInterval(interval);
      console.log('File upload loading dialog closed:', result);
      const dialogResult = result as { action?: string };
      if (dialogResult.action === 'cancel') {
        console.log('File upload was cancelled');
      }
    });
  }
}
