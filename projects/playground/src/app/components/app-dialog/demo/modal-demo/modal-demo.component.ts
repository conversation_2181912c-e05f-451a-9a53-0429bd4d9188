import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';
import { DialogService } from '../../../../../../../play-comp-library/src/lib/components/dialog/dialog-service';

@Component({
  selector: 'app-modal-demo',
  standalone: true,
  imports: [CommonModule, ButtonComponent],
  template: `
    <div class="demo-container">
      <div class="demo-header">
        <h2>Modal Dialogs</h2>
        <p>
          Advanced modal dialog support with content projection and flexible
          layouts.
        </p>
      </div>

      <div class="demo-content">
        <div class="demo-section">
          <h3>Basic Modal</h3>
          <p>Simple modal with custom content and close button.</p>
          <ava-button
            label="Open Basic Modal"
            variant="primary"
            (userClick)="openBasicModal()"
          ></ava-button>
        </div>

        <div class="demo-section">
          <h3>Large Modal</h3>
          <p>Modal with larger dimensions for more content.</p>
          <ava-button
            label="Open Large Modal"
            variant="secondary"
            (userClick)="openLargeModal()"
          ></ava-button>
        </div>

        <div class="demo-section">
          <h3>Custom Width Modal</h3>
          <p>Modal with custom width and responsive behavior.</p>
          <ava-button
            label="Open Custom Width Modal"
            variant="info"
            (userClick)="openCustomWidthModal()"
          ></ava-button>
        </div>

        <div class="demo-section">
          <h3>Modal with Data</h3>
          <p>Modal that receives and displays custom data.</p>
          <ava-button
            label="Open Modal with Data"
            variant="success"
            (userClick)="openModalWithData()"
          ></ava-button>
        </div>

        <div class="demo-section">
          <h3>Modal without Backdrop</h3>
          <p>Modal that can be closed only via close button.</p>
          <ava-button
            label="Open Modal without Backdrop"
            variant="warning"
            (userClick)="openModalWithoutBackdrop()"
          ></ava-button>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
      }

      .demo-header {
        text-align: center;
        margin-bottom: 3rem;
      }

      .demo-header h2 {
        color: var(--color-text-primary);
        margin-bottom: 1rem;
        font-size: 2rem;
      }

      .demo-header p {
        color: var(--color-text-secondary);
        font-size: 1.1rem;
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
      }

      .demo-content {
        display: flex;
        flex-direction: column;
        gap: 2rem;
      }

      .demo-section {
        background: var(--color-background-secondary);
        border: 1px solid var(--color-border);
        border-radius: 8px;
        padding: 1.5rem;
        text-align: center;
      }

      .demo-section h3 {
        color: var(--color-text-primary);
        margin-bottom: 0.5rem;
        font-size: 1.3rem;
      }

      .demo-section p {
        color: var(--color-text-secondary);
        margin-bottom: 1rem;
        line-height: 1.5;
      }

      ava-button {
        margin: 0.5rem;
      }
    `,
  ],
})
export class ModalDemoComponent {
  constructor(private dialogService: DialogService) {}

  openBasicModal() {
    this.dialogService
      .openModal(ModalContentComponent, {
        width: '500px',
        maxWidth: '90vw',
        showCloseButton: true,
      })
      .then((result: unknown) => {
        console.log('Basic modal closed:', result);
      });
  }

  openLargeModal() {
    this.dialogService
      .openModal(ModalContentComponent, {
        width: '800px',
        maxWidth: '95vw',
        showCloseButton: true,
      })
      .then((result: unknown) => {
        console.log('Large modal closed:', result);
      });
  }

  openCustomWidthModal() {
    this.dialogService
      .openModal(ModalContentComponent, {
        width: '600px',
        maxWidth: '90vw',
        showCloseButton: true,
      })
      .then((result: unknown) => {
        console.log('Custom width modal closed:', result);
      });
  }

  openModalWithData() {
    this.dialogService
      .openModal(
        ModalContentComponent,
        {
          width: '600px',
          maxWidth: '90vw',
          showCloseButton: true,
        },
        {
          data: {
            id: 123,
            name: 'Example Item',
            description: 'This is sample data passed to the modal.',
          },
        }
      )
      .then((result: unknown) => {
        console.log('Modal with data closed:', result);
      });
  }

  openModalWithoutBackdrop() {
    this.dialogService
      .openModal(ModalContentComponent, {
        width: '500px',
        maxWidth: '90vw',
        showCloseButton: true,
        backdrop: false,
      })
      .then((result: unknown) => {
        console.log('Modal without backdrop closed:', result);
      });
  }
}

// Simple modal content component
@Component({
  selector: 'app-modal-content',
  standalone: true,
  imports: [CommonModule, ButtonComponent],
  template: `
    <div class="modal-content">
      <div class="modal-header">
        <h3>Modal Content</h3>
      </div>
      <div class="modal-body">
        <p>This is the content of the modal dialog.</p>
        <p *ngIf="data">Received data: {{ data.name }} (ID: {{ data.id }})</p>
        <p>{{ data?.description }}</p>
      </div>
      <div class="modal-footer">
        <ava-button
          label="Close"
          variant="secondary"
          (userClick)="close()"
        ></ava-button>
      </div>
    </div>
  `,
  styles: [
    `
      .modal-content {
        padding: 1rem;
      }

      .modal-header {
        margin-bottom: 1rem;
        border-bottom: 1px solid var(--color-border);
        padding-bottom: 1rem;
      }

      .modal-header h3 {
        color: var(--color-text-primary);
        margin: 0;
        font-size: 1.5rem;
      }

      .modal-body {
        margin-bottom: 1.5rem;
        line-height: 1.6;
      }

      .modal-body p {
        color: var(--color-text-secondary);
        margin-bottom: 0.5rem;
      }

      .modal-footer {
        text-align: right;
        border-top: 1px solid var(--color-border);
        padding-top: 1rem;
      }
    `,
  ],
})
export class ModalContentComponent {
  data: any;

  constructor(private dialogService: DialogService) {
    // Get data passed to the modal
    this.data = (this.dialogService as any).getModalData?.();
  }

  close() {
    this.dialogService.close();
  }
}
