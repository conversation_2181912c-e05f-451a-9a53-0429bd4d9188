import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AavaTooltipDirective } from '../../../../../../../play-comp-library/src/lib/directives/aava-tooltip.directive';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';

@Component({
  selector: 'ava-tooltip-basic-usage-demo',
  standalone: true,
  imports: [CommonModule, AavaTooltipDirective, ButtonComponent],
  template: `
    <div class="demo-container">
      <div class="demo-section">
        <div class="demo-grid">
          <ava-button
            label="Tooltip with Hover"
            variant="primary"
            size="small"
            state="default"
            aavaTooltipDescription="Tooltip with Hover"
            aavaTooltipType="simple"
            aavaTooltipTrigger="hover"
            aavaTooltipPosition="top"
            aavaTooltipArrow="center"
          >
          </ava-button>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
        margin-top: 3rem;
      }
      .demo-description {
        color: #666;
        margin-bottom: 30px;
        font-size: 16px;
      }
      .demo-grid {
        text-align: center;
        gap: 2rem;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
      }
      .tooltip-text {
        padding: 2px 6px;
        border-radius: 4px;
        cursor: help;
        border-bottom: 1px dotted #1976d2;
        color: #1976d2;
      }
    `,
  ],
})
export class TooltipBasicUsageDemoComponent {}
