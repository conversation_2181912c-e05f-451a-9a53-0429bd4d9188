import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AavaTooltipDirective } from '../../../../../../../play-comp-library/src/lib/directives/aava-tooltip.directive';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';

@Component({
  selector: 'ava-tooltip-behaviors-demo',
  standalone: true,
  imports: [CommonModule, AavaTooltipDirective, ButtonComponent],
  template: `
    <div class="demo-container">
      <h2>Tooltip Behaviors</h2>
      <p class="demo-description">
        Tooltip can be triggered on hover, focus, or other behaviors. Choose the
        right trigger based on your use case and accessibility requirements.
      </p>

      <div class="demo-section">
        <h3>Hover Trigger (Default)</h3>
        <div class="behavior-demo">
          <ava-button
            label="Hover Me"
            variant="primary"
            size="medium"
            aavaTooltipDescription="This tooltip appears on hover and disappears when you move away"
            aavaTooltipTrigger="hover"
            aavaTooltipPosition="top"
          >
          </ava-button>
          <p class="behavior-description">
            <strong>Best for:</strong> General information, non-critical
            context, desktop interactions
          </p>
        </div>
      </div>

      <div class="demo-section">
        <h3>Click Trigger</h3>
        <div class="behavior-demo">
          <ava-button
            label="Click Me"
            variant="secondary"
            size="medium"
            aavaTooltipDescription="This tooltip appears on click and stays until you click elsewhere"
            aavaTooltipTrigger="click"
            aavaTooltipPosition="top"
          >
          </ava-button>
          <p class="behavior-description">
            <strong>Best for:</strong> Mobile devices, important information,
            persistent tooltips
          </p>
        </div>
      </div>

      <div class="demo-section">
        <h3>Focus Trigger</h3>
        <div class="behavior-demo">
          <div class="focus-examples">
            <input
              type="text"
              class="focus-input"
              placeholder="Tab to focus input"
              aavaTooltipDescription="This tooltip appears when the input receives focus"
              aavaTooltipTrigger="focus"
              aavaTooltipPosition="right"
            />

            <button
              class="focus-button"
              aavaTooltipDescription="Keyboard accessible tooltip"
              aavaTooltipTrigger="focus"
              aavaTooltipPosition="top"
            >
              Tab to Focus Button
            </button>
          </div>
          <p class="behavior-description">
            <strong>Best for:</strong> Form elements, keyboard navigation,
            accessibility compliance
          </p>
        </div>
      </div>

      <div class="demo-section">
        <h3>Advanced Behaviors</h3>
        <div class="behavior-demo">
          <div class="manual-controls">
            <ava-button
              label="Programmatic Control"
              variant="secondary"
              size="medium"
              aavaTooltipDescription="This tooltip can be controlled programmatically"
              aavaTooltipPosition="top"
            >
            </ava-button>

            <div class="manual-buttons">
              <ava-button
                label="Show Info"
                variant="success"
                size="small"
                (click)="showTooltipInfo()"
              >
              </ava-button>
              <ava-button
                label="Hide Info"
                variant="danger"
                size="small"
                (click)="hideTooltipInfo()"
              >
              </ava-button>
            </div>
          </div>
          <p class="behavior-description">
            <strong>Best for:</strong> Programmatic control, guided tours,
            conditional tooltips
          </p>
        </div>
      </div>

      <div class="demo-section">
        <h3>Behavior Comparison</h3>
        <div class="comparison-table">
          <table>
            <thead>
              <tr>
                <th>Trigger</th>
                <th>Activation</th>
                <th>Accessibility</th>
                <th>Mobile Friendly</th>
                <th>Use Case</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><strong>Hover</strong></td>
                <td>Mouse enter/leave</td>
                <td>⚠️ Limited</td>
                <td>❌ No</td>
                <td>Desktop context</td>
              </tr>
              <tr>
                <td><strong>Click</strong></td>
                <td>Mouse click</td>
                <td>✅ Good</td>
                <td>✅ Yes</td>
                <td>Persistent info</td>
              </tr>
              <tr>
                <td><strong>Focus</strong></td>
                <td>Keyboard/mouse focus</td>
                <td>✅ Excellent</td>
                <td>✅ Yes</td>
                <td>Form elements</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div class="demo-section">
        <h3>Mixed Behaviors Example</h3>
        <div class="mixed-demo">
          <div class="form-example">
            <input
              type="text"
              class="demo-input"
              placeholder="Enter username"
              aavaTooltipDescription="Username must be 3-20 characters, alphanumeric only"
              aavaTooltipTrigger="focus"
              aavaTooltipPosition="right"
            />

            <ava-button
              label="Submit"
              variant="primary"
              size="medium"
              aavaTooltipDescription="Click to submit the form"
              aavaTooltipTrigger="hover"
              aavaTooltipPosition="top"
            >
            </ava-button>

            <span
              class="help-icon"
              aavaTooltipDescription="Click for detailed help information"
              aavaTooltipTrigger="click"
              aavaTooltipPosition="left"
            >
              ❓
            </span>
          </div>
        </div>
      </div>

      <div class="code-example">
        <h3>Code Example</h3>
        <pre><code>{{ codeExample }}</code></pre>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 900px;
        margin: 0 auto;
        padding: 2rem;
      }

      .demo-description {
        color: #666;
        margin-bottom: 30px;
        font-size: 16px;
      }

      .demo-section {
        margin-bottom: 40px;
      }

      .demo-section h3 {
        color: #333;
        margin-bottom: 20px;
        font-size: 18px;
      }

      .behavior-demo {
        padding: 1.5rem;
        background-color: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #007bff;
      }

      .behavior-description {
        margin-top: 1rem;
        color: #666;
        font-size: 14px;
        line-height: 1.4;
        margin-bottom: 0;
      }

      .focus-examples {
        display: flex;
        gap: 1rem;
        align-items: center;
        flex-wrap: wrap;
      }

      .focus-input,
      .demo-input {
        padding: 8px 12px;
        border: 2px solid #dee2e6;
        border-radius: 4px;
        font-size: 14px;
      }

      .focus-input:focus,
      .demo-input:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
      }

      .focus-button {
        padding: 8px 16px;
        background-color: #6c757d;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
      }

      .focus-button:focus {
        outline: 2px solid #007bff;
        outline-offset: 2px;
      }

      .manual-controls {
        display: flex;
        gap: 1rem;
        align-items: center;
        flex-wrap: wrap;
      }

      .manual-buttons {
        display: flex;
        gap: 0.5rem;
      }

      .comparison-table {
        overflow-x: auto;
      }

      table {
        width: 100%;
        border-collapse: collapse;
        background-color: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      th,
      td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #dee2e6;
      }

      th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #495057;
      }

      tbody tr:hover {
        background-color: #f8f9fa;
      }

      .mixed-demo {
        padding: 1.5rem;
        background-color: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #dee2e6;
      }

      .form-example {
        display: flex;
        gap: 1rem;
        align-items: center;
        flex-wrap: wrap;
      }

      .help-icon {
        font-size: 20px;
        cursor: pointer;
        padding: 8px;
        border-radius: 50%;
        background-color: #17a2b8;
        color: white;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
      }

      .code-example {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border-left: 4px solid #007bff;
      }

      .code-example h3 {
        margin-top: 0;
        color: #333;
      }

      pre {
        background-color: #2d3748;
        color: #e2e8f0;
        padding: 16px;
        border-radius: 6px;
        overflow-x: auto;
        margin: 0;
      }

      code {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 14px;
      }

      @media (max-width: 768px) {
        .focus-examples,
        .manual-controls,
        .form-example {
          flex-direction: column;
          align-items: stretch;
        }

        table {
          font-size: 12px;
        }

        th,
        td {
          padding: 8px;
        }
      }
    `,
  ],
})
export class TooltipBehaviorsDemoComponent {
  showTooltipInfo(): void {
    console.log('Show tooltip info');
  }

  hideTooltipInfo(): void {
    console.log('Hide tooltip info');
  }

  codeExample = `<!-- Hover trigger (default) -->
<ava-button
  label="Hover"
  aavaTooltipDescription="Appears on hover"
  aavaTooltipTrigger="hover">
</ava-button>

<!-- Click trigger -->
<ava-button
  label="Click"
  aavaTooltipDescription="Appears on click"
  aavaTooltipTrigger="click">
</ava-button>

<!-- Focus trigger -->
<input
  aavaTooltipDescription="Appears on focus"
  aavaTooltipTrigger="focus">

<!-- Programmatic control -->
<ava-button
  label="Controlled"
  aavaTooltipDescription="Controlled programmatically">
</ava-button>`;
}
