import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AavaTooltipDirective } from '../../../../../../../play-comp-library/src/lib/directives/aava-tooltip.directive';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';

@Component({
  selector: 'ava-tooltip-positions-demo',
  standalone: true,
  imports: [CommonModule, AavaTooltipDirective, ButtonComponent],
  template: `
    <div class="demo-container">
      <div class="positions-grid">
        <ava-button
          label=" Start"
          variant="primary"
          size="small"
          state="default"
          aavaTooltipDescription=" Start"
          aavaTooltipType="simple"
          aavaTooltipTrigger="hover"
          aavaTooltipPosition="top"
          aavaTooltipArrow="start"
        ></ava-button>
        <ava-button
          label=" Center"
          variant="primary"
          size="small"
          state="default"
          aavaTooltipDescription=" Center"
          aavaTooltipType="simple"
          aavaTooltipTrigger="hover"
          aavaTooltipPosition="top"
          aavaTooltipArrow="center"
        ></ava-button>
        <ava-button
          label=" End"
          variant="primary"
          size="small"
          state="default"
          aavaTooltipDescription=" End"
          aavaTooltipType="simple"
          aavaTooltipTrigger="hover"
          aavaTooltipPosition="top"
          aavaTooltipArrow="end"
        ></ava-button>
        <!-- <ava-button
          label="Left Start"
          variant="primary"
          size="medium"
          state="default"
          aavaTooltipDescription="Left Start"
          aavaTooltipType="simple"
          aavaTooltipTrigger="hover"
          aavaTooltipPosition="left"
          aavaTooltipArrow="start"
        ></ava-button>
        <ava-button
          label="Left Center"
          variant="primary"
          size="medium"
          state="default"
          aavaTooltipDescription="Left Center"
          aavaTooltipType="simple"
          aavaTooltipTrigger="hover"
          aavaTooltipPosition="left"
          aavaTooltipArrow="center"
        ></ava-button>
        <ava-button
          label="Left End"
          variant="primary"
          size="medium"
          state="default"
          aavaTooltipDescription="Left End"
          aavaTooltipType="simple"
          aavaTooltipTrigger="hover"
          aavaTooltipPosition="left"
          aavaTooltipArrow="end"
        ></ava-button>
        <ava-button
          label="Right Start"
          variant="primary"
          size="medium"
          state="default"
          aavaTooltipDescription="Right Start"
          aavaTooltipType="simple"
          aavaTooltipTrigger="hover"
          aavaTooltipPosition="right"
          aavaTooltipArrow="start"
        ></ava-button>
        <ava-button
          label="Right Center"
          variant="primary"
          size="medium"
          state="default"
          aavaTooltipDescription="Right Center"
          aavaTooltipType="simple"
          aavaTooltipTrigger="hover"
          aavaTooltipPosition="right"
          aavaTooltipArrow="center"
        ></ava-button>
        <ava-button
          label="Right End"
          variant="primary"
          size="medium"
          state="default"
          aavaTooltipDescription="Right End"
          aavaTooltipType="simple"
          aavaTooltipTrigger="hover"
          aavaTooltipPosition="right"
          aavaTooltipArrow="end"
        ></ava-button>
        <ava-button
          label="Bottom Start"
          variant="primary"
          size="medium"
          state="default"
          aavaTooltipDescription="Bottom Start"
          aavaTooltipType="simple"
          aavaTooltipTrigger="hover"
          aavaTooltipPosition="bottom"
          aavaTooltipArrow="start"
        ></ava-button>
        <ava-button
          label="Bottom Center"
          variant="primary"
          size="medium"
          state="default"
          aavaTooltipDescription="Bottom Center"
          aavaTooltipType="simple"
          aavaTooltipTrigger="hover"
          aavaTooltipPosition="bottom"
          aavaTooltipArrow="center"
        ></ava-button>
        <ava-button
          label="Bottom End"
          variant="primary"
          size="medium"
          state="default"
          aavaTooltipDescription="Bottom End"
          aavaTooltipType="simple"
          aavaTooltipTrigger="hover"
          aavaTooltipPosition="bottom"
          aavaTooltipArrow="end"
        ></ava-button> -->
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
        margin-top: 3rem;
      }
      .positions-grid {
        text-align: center;
        gap: 2rem;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
      }
    `,
  ],
})
export class TooltipPositionsDemoComponent {}
