import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PopoverDirective } from '../../../../../../../play-comp-library/src/lib/directives/popover.directive';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';
import { PopOverData } from '../../../../../../../play-comp-library/src/lib/components/pop-over/pop-over.component';

@Component({
  selector: 'ava-basic-usage-demo',
  standalone: true,
  imports: [CommonModule, PopoverDirective, ButtonComponent],
  template: `
    <div class="demo-container">
      <div class="demo-content">
        <div class="demo-section">
          <ava-button
            avaPopover
            [avaPopoverData]="samplePopoverData"
            [avaPopoverPosition]="'top'"
            [avaPopoverArrow]="'center'"
            [label]="'Show Popover'"
            [variant]="'primary'"
            [outlined]="true"
            pressedEffect="ripple"
          ></ava-button>
        </div>

        <div class="demo-section">
          <ava-button
            avaPopover
            [avaPopoverData]="learnMorePopoverData"
            [avaPopoverPosition]="'bottom'"
            [avaPopoverArrow]="'center'"
            [avaPopoverShowLearnMore]="true"
            [label]="'Show with Learn More'"
            [variant]="'secondary'"
            [outlined]="true"
            pressedEffect="ripple"
          ></ava-button>
        </div>

        <div class="demo-section">
          <ava-button
            avaPopover
            [avaPopoverData]="multiStepPopoverData"
            [avaPopoverPosition]="'right'"
            [avaPopoverArrow]="'center'"
            [avaPopoverShowButtons]="true"
            [avaPopoverShowPagination]="true"
            [label]="'Show Multi-step'"
            [variant]="'success'"
            [outlined]="true"
            pressedEffect="ripple"
          ></ava-button>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
        margin-top: 50px;
      }

      .demo-content {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
      }

      .demo-section {
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100px;
      }

      ava-button {
        margin-top: auto;
      }
    `,
  ],
})
export class BasicUsageDemoComponent {
  samplePopoverData: PopOverData[] = [
    {
      header: 'Welcome!',
      description: 'This is a simple popover with basic content.',
    },
  ];

  learnMorePopoverData: PopOverData[] = [
    {
      header: 'Learn More',
      description:
        'This popover includes a learn more link for additional information.',
      learnMoreUrl: 'https://example.com',
    },
  ];

  multiStepPopoverData: PopOverData[] = [
    {
      header: 'Step 1',
      description: 'This is the first step of the multi-step popover.',
    },
    {
      header: 'Step 2',
      description: 'This is the second step with additional information.',
    },
    {
      header: 'Step 3',
      description: 'This is the final step with completion details.',
    },
  ];
}
