.popover-demo-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;

  .header {
    text-align: center;
    margin-bottom: 3rem;

    h1 {
      color: var(--color-text-primary);
      margin-bottom: 1rem;
      font-size: 2.5rem;
    }

    p {
      color: var(--color-text-secondary);
      font-size: 1.1rem;
      max-width: 800px;
      margin: 0 auto;
      line-height: 1.6;
    }
  }

  .demo-links {
    margin-bottom: 3rem;

    h3 {
      color: var(--color-text-primary);
      margin-bottom: 1.5rem;
      font-size: 1.5rem;
      text-align: center;
    }

    .demo-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;
    }

    .demo-link {
      text-decoration: none;
      color: inherit;
    }

    .demo-card {
      background: var(--color-background-secondary);
      border: 1px solid var(--color-border);
      border-radius: 8px;
      padding: 1.5rem;
      text-align: center;
      transition: all 0.3s ease;
      min-height: 150px;
      display: flex;
      flex-direction: column;
      justify-content: center;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border-color: var(--color-primary);
      }

      h4 {
        color: var(--color-text-primary);
        margin-bottom: 0.5rem;
        font-size: 1.2rem;
      }

      p {
        color: var(--color-text-secondary);
        line-height: 1.5;
      }
    }
  }

  .working-demos {
    margin-bottom: 3rem;

    h2 {
      color: var(--color-text-primary);
      margin-bottom: 1rem;
      font-size: 1.8rem;
      text-align: center;
    }

    p {
      color: var(--color-text-secondary);
      text-align: center;
      margin-bottom: 2rem;
      font-size: 1.1rem;
    }
  }

  .demo-section {
    margin-bottom: 3rem;
    padding: 1.5rem;
    border: 1px solid var(--color-border-default);
    border-radius: var(--global-radius-md);
    background: var(--color-background-secondary);

    h2 {
      color: var(--color-text-primary);
      margin-bottom: 1.5rem;
      font-size: 1.25rem;
    }

    h3 {
      color: var(--color-text-secondary);
      margin-bottom: 0.75rem;
      font-size: 1rem;
      font-weight: 500;
    }
  }

  .demo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  .demo-item {
    padding: 1rem;
    border: 1px solid var(--color-border-subtle);
    border-radius: var(--global-radius-sm);
    background: var(--color-background-primary);
    text-align: center;
  }

  .position-demo {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
    min-height: 200px;
  }

  .arrow-demo {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
    min-height: 150px;
  }

  .variant-demo {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
    min-height: 150px;
  }

  .trigger-demo {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
    min-height: 150px;
  }

  .tour-trigger {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: var(--color-background-accent);
    color: var(--color-text-on-accent);
    border-radius: var(--global-radius-sm);
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background: var(--color-background-accent-hover);
    }

    span {
      font-weight: 500;
    }
  }

  .setup-guide {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: var(--color-background-info);
    color: var(--color-text-on-info);
    border-radius: var(--global-radius-sm);
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s ease;

    &:hover {
      background: var(--color-background-info-hover);
    }
  }

  .features-section {
    margin-bottom: 3rem;

    h3 {
      color: var(--color-text-primary);
      margin-bottom: 1.5rem;
      font-size: 1.5rem;
      text-align: center;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;
    }

    .feature-item {
      background: var(--color-background-secondary);
      border: 1px solid var(--color-border);
      border-radius: 8px;
      padding: 1.5rem;
      text-align: center;

      h4 {
        color: var(--color-text-primary);
        margin-bottom: 0.5rem;
        font-size: 1.1rem;
      }

      p {
        color: var(--color-text-secondary);
        line-height: 1.5;
      }
    }
  }
}
