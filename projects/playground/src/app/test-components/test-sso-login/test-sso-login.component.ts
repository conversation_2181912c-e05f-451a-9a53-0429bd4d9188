import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SSOLoginComponent } from '../../../../../play-comp-library/src/lib/composite-components/sso-login/sso-login.component';

@Component({
  selector: 'app-test-sso-login',
  standalone: true,
  imports: [
    CommonModule,
    SSOLoginComponent
  ],
  templateUrl: './test-sso-login.component.html',
  styleUrl: './test-sso-login.component.scss'
})
export class TestSSOLoginComponent {
  // Simple test component for SSO login variants
}
