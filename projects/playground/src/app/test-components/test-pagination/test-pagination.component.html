<div class="t-container">
    <!-- Basic Variant -->
    <div class="con">
        <div class="col-12 col-sm-auto">
            <ava-pagination-controls [type]="'basic'" [currentPage]="basicPage" [totalPages]="10" [size]="'xlarge'" (pageChange)="onPageChange('basic', $event)"></ava-pagination-controls>
            <ava-pagination-controls [type]="'basic'" [currentPage]="basicPage" [totalPages]="10" [size]="'large'" (pageChange)="onPageChange('basic', $event)"></ava-pagination-controls>
            <ava-pagination-controls [type]="'basic'" [currentPage]="basicPage" [totalPages]="10" [size]="'medium'" (pageChange)="onPageChange('basic', $event)"></ava-pagination-controls>
            <ava-pagination-controls [type]="'basic'" [currentPage]="basicPage" [totalPages]="10" [size]="'small'" (pageChange)="onPageChange('basic', $event)"></ava-pagination-controls>
            <ava-pagination-controls [type]="'basic'" [currentPage]="basicPage" [totalPages]="10" [size]="'xsmall'" (pageChange)="onPageChange('basic', $event)"></ava-pagination-controls>
        </div>
    </div>

    <!-- Unfilled Variant -->
    <div class="con">
        <div class="col-12 col-sm-auto">
            <ava-pagination-controls [type]="'unfilled'" [currentPage]="unfilledPage" [totalPages]="10" [size]="'xlarge'" (pageChange)="onPageChange('unfilled', $event)"></ava-pagination-controls>
            <ava-pagination-controls [type]="'unfilled'" [currentPage]="unfilledPage" [totalPages]="10" [size]="'large'" (pageChange)="onPageChange('unfilled', $event)"></ava-pagination-controls>
            <ava-pagination-controls [type]="'unfilled'" [currentPage]="unfilledPage" [totalPages]="10" [size]="'medium'" (pageChange)="onPageChange('unfilled', $event)"></ava-pagination-controls>
            <ava-pagination-controls [type]="'unfilled'" [currentPage]="unfilledPage" [totalPages]="10" [size]="'small'" (pageChange)="onPageChange('unfilled', $event)"></ava-pagination-controls>
            <ava-pagination-controls [type]="'unfilled'" [currentPage]="unfilledPage" [totalPages]="10" [size]="'xsmall'" (pageChange)="onPageChange('unfilled', $event)"></ava-pagination-controls>
        </div>
    </div>

    <!-- Basic Unfilled Variant -->
    <div class="con">
        <div class="col-12 col-sm-auto">
            <ava-pagination-controls [type]="'basicunfilled'" [currentPage]="basicUnfilledPage" [totalPages]="10" [size]="'xlarge'" (pageChange)="onPageChange('basicunfilled', $event)"></ava-pagination-controls>
            <ava-pagination-controls [type]="'basicunfilled'" [currentPage]="basicUnfilledPage" [totalPages]="10" [size]="'large'" (pageChange)="onPageChange('basicunfilled', $event)"></ava-pagination-controls>
            <ava-pagination-controls [type]="'basicunfilled'" [currentPage]="basicUnfilledPage" [totalPages]="10" [size]="'medium'" (pageChange)="onPageChange('basicunfilled', $event)"></ava-pagination-controls>
            <ava-pagination-controls [type]="'basicunfilled'" [currentPage]="basicUnfilledPage" [totalPages]="10" [size]="'small'" (pageChange)="onPageChange('basicunfilled', $event)"></ava-pagination-controls>
            <ava-pagination-controls [type]="'basicunfilled'" [currentPage]="basicUnfilledPage" [totalPages]="10" [size]="'xsmall'" (pageChange)="onPageChange('basicunfilled', $event)"></ava-pagination-controls>
        </div>
    </div>

    <!-- Page Info Variant -->
    <div class="con">
        <div class="col-12 col-sm-auto">
            <ava-pagination-controls [type]="'pageinfo'" [currentPage]="pageInfoPage" [totalPages]="10" [size]="'xlarge'" (pageChange)="onPageChange('pageinfo', $event)"></ava-pagination-controls>
            <ava-pagination-controls [type]="'pageinfo'" [currentPage]="pageInfoPage" [totalPages]="10" [size]="'large'" (pageChange)="onPageChange('pageinfo', $event)"></ava-pagination-controls>
            <ava-pagination-controls [type]="'pageinfo'" [currentPage]="pageInfoPage" [totalPages]="10" [size]="'medium'" (pageChange)="onPageChange('pageinfo', $event)"></ava-pagination-controls>
            <ava-pagination-controls [type]="'pageinfo'" [currentPage]="pageInfoPage" [totalPages]="10" [size]="'small'" (pageChange)="onPageChange('pageinfo', $event)"></ava-pagination-controls>
            <ava-pagination-controls [type]="'pageinfo'" [currentPage]="pageInfoPage" [totalPages]="10" [size]="'xsmall'" (pageChange)="onPageChange('pageinfo', $event)"></ava-pagination-controls>
        </div>
    </div>

    <!-- Page Info Filled Variant -->
    <div class="con">
        <div class="col-12 col-sm-auto">
            <ava-pagination-controls [type]="'pageinfofilled'" [currentPage]="pageInfoFilledPage" [totalPages]="10" [size]="'xlarge'" (pageChange)="onPageChange('pageinfofilled', $event)"></ava-pagination-controls>
            <ava-pagination-controls [type]="'pageinfofilled'" [currentPage]="pageInfoFilledPage" [totalPages]="10" [size]="'large'" (pageChange)="onPageChange('pageinfofilled', $event)"></ava-pagination-controls>
            <ava-pagination-controls [type]="'pageinfofilled'" [currentPage]="pageInfoFilledPage" [totalPages]="10" [size]="'medium'" (pageChange)="onPageChange('pageinfofilled', $event)"></ava-pagination-controls>
            <ava-pagination-controls [type]="'pageinfofilled'" [currentPage]="pageInfoFilledPage" [totalPages]="10" [size]="'small'" (pageChange)="onPageChange('pageinfofilled', $event)"></ava-pagination-controls>
            <ava-pagination-controls [type]="'pageinfofilled'" [currentPage]="pageInfoFilledPage" [totalPages]="10" [size]="'xsmall'" (pageChange)="onPageChange('pageinfofilled', $event)"></ava-pagination-controls>
        </div>
    </div>
</div>
