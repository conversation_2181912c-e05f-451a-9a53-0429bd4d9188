<div class="test-accordion-container">
    <div class="accordion-grid">
        <div class="accordion-section basic-variants">

            <div class="section-title">Size Variants</div>

            <div class="accordion-wrapper">
                <ava-accordion type="default" iconClosed="chevron-down" iconOpen="chevron-up" iconPosition="left"
                    (click)="onAccordionToggle($event)" size="lg">
                    <span header>Left Icon Position</span>
                    <div content>
                        <p>
                            This is the default accordion type with expandable icons positioned on
                            the left side.
                        </p>
                        <p>
                            It provides a clean, traditional accordion experience with smooth
                            animations.
                        </p>
                    </div>
                </ava-accordion>
            </div>

            <div class="accordion-wrapper">
                <ava-accordion type="default" iconClosed="chevron-down" iconOpen="chevron-up" iconPosition="left"
                    (click)="onAccordionToggle($event)" size="md">
                    <span header>Left Icon Position</span>
                    <div content>
                        <p>
                            This is the default accordion type with expandable icons positioned on
                            the left side.
                        </p>
                        <p>
                            It provides a clean, traditional accordion experience with smooth
                            animations.
                        </p>
                    </div>
                </ava-accordion>
            </div>

            <div class="accordion-wrapper">
                <ava-accordion type="default" iconClosed="chevron-down" iconOpen="chevron-up" iconPosition="left"
                    (click)="onAccordionToggle($event)" size="sm">
                    <span header>Left Icon Position</span>
                    <div content>
                        <p>
                            This is the default accordion type with expandable icons positioned on
                            the left side.
                        </p>
                        <p>
                            It provides a clean, traditional accordion experience with smooth
                            animations.
                        </p>
                    </div>
                </ava-accordion>
            </div>

            <div class="accordion-wrapper">
                <ava-accordion type="default" iconClosed="chevron-down" iconOpen="chevron-up" iconPosition="right"
                    (click)="onAccordionToggle($event)" size="lg">
                    <span header>Right Icon Position</span>
                    <div content>
                        <p>
                            This accordion has icons positioned on the right side for a different
                            visual layout.
                        </p>
                        <p>
                            Perfect for layouts where you want the icons to be more prominent
                            on the right.
                        </p>
                    </div>
                </ava-accordion>
            </div>

            <div class="accordion-wrapper">
                <ava-accordion type="default" iconClosed="chevron-down" iconOpen="chevron-up" iconPosition="right"
                    (click)="onAccordionToggle($event)" size="md">
                    <span header>Right Icon Position</span>
                    <div content>
                        <p>
                            This accordion has icons positioned on the right side for a different
                            visual layout.
                        </p>
                        <p>
                            Perfect for layouts where you want the icons to be more prominent
                            on the right.
                        </p>
                    </div>
                </ava-accordion>
            </div>

            <div class="accordion-wrapper">
                <ava-accordion type="default" iconClosed="chevron-down" iconOpen="chevron-up" iconPosition="right"
                    (click)="onAccordionToggle($event)" size="sm">
                    <span header>Right Icon Position</span>
                    <div content>
                        <p>
                            This accordion has icons positioned on the right side for a different
                            visual layout.
                        </p>
                        <p>
                            Perfect for layouts where you want the icons to be more prominent
                            on the right.
                        </p>
                    </div>
                </ava-accordion>
            </div>

            <div class="accordion-wrapper">
                <ava-accordion type="default" iconClosed="chevron-down" iconOpen="chevron-up" iconPosition="left"
                    [withoutBox]="true" (click)="onAccordionToggle($event)" size="lg">
                    <span header>Without Box Styling</span>
                    <div content>
                        <p>
                            This accordion variant removes the box styling for a cleaner,
                            borderless appearance.
                        </p>
                        <p>
                            Ideal for minimalist designs or when you want the content to blend
                            seamlessly with the background.
                        </p>
                    </div>
                </ava-accordion>
            </div>

            <div class="accordion-wrapper">
                <ava-accordion type="default" iconClosed="chevron-down" iconOpen="chevron-up" iconPosition="left"
                    [withoutBox]="true" (click)="onAccordionToggle($event)" size="md">
                    <span header>Without Box Styling</span>
                    <div content>
                        <p>
                            This accordion variant removes the box styling for a cleaner,
                            borderless appearance.
                        </p>
                        <p>
                            Ideal for minimalist designs or when you want the content to blend
                            seamlessly with the background.
                        </p>
                    </div>
                </ava-accordion>
            </div>

            <div class="accordion-wrapper">
                <ava-accordion type="default" iconClosed="chevron-down" iconOpen="chevron-up" iconPosition="left"
                    [withoutBox]="true" (click)="onAccordionToggle($event)" size="sm">
                    <span header>Without Box Styling</span>
                    <div content>
                        <p>
                            This accordion variant removes the box styling for a cleaner,
                            borderless appearance.
                        </p>
                        <p>
                            Ideal for minimalist designs or when you want the content to blend
                            seamlessly with the background.
                        </p>
                    </div>
                </ava-accordion>
            </div>

            <div class="accordion-wrapper">
                <ava-accordion iconClosed="chevron-down" iconOpen="chevron-up" size="lg">
                    <div header>
                        <div style="display: flex; align-items: center; gap: 8px">
                            <span style="font-size: 20px">📊</span>
                            <span>Analytics Dashboard</span>
                        </div>
                    </div>
                    <div content>
                        <div class="dashboard-content">
                            <h5>Dashboard Content</h5>
                            <p>Rich content with custom styling and layout. This demonstrates how accordions can contain
                                complex UI elements.</p>
                            <button class="btn">View Details</button>
                        </div>
                    </div>
                </ava-accordion>
            </div>

            <div class="accordion-wrapper">
                <ava-accordion iconClosed="chevron-down" iconOpen="chevron-up" size="md">
                    <div header>
                        <div style="display: flex; align-items: center; gap: 8px">
                            <span style="font-size: 20px">📊</span>
                            <span>Analytics Dashboard</span>
                        </div>
                    </div>
                    <div content>
                        <div class="dashboard-content">
                            <h5>Dashboard Content</h5>
                            <p>Rich content with custom styling and layout. This demonstrates how accordions can contain
                                complex UI elements.</p>
                            <button class="btn">View Details</button>
                        </div>
                    </div>
                </ava-accordion>
            </div>

            <div class="accordion-wrapper">
                <ava-accordion iconClosed="chevron-down" iconOpen="chevron-up" size="sm">
                    <div header>
                        <div style="display: flex; align-items: center; gap: 8px">
                            <span style="font-size: 20px">📊</span>
                            <span>Analytics Dashboard</span>
                        </div>
                    </div>
                    <div content>
                        <div class="dashboard-content">
                            <h5>Dashboard Content</h5>
                            <p>Rich content with custom styling and layout. This demonstrates how accordions can contain
                                complex UI elements.</p>
                            <button class="btn">View Details</button>
                        </div>
                    </div>
                </ava-accordion>
            </div>

            <div class="section-title">Basic Variants</div>

            <div class="accordion-wrapper">
                <ava-accordion type="default" iconClosed="chevron-down" iconOpen="chevron-up" iconPosition="left"
                    (click)="onAccordionToggle($event)">
                    <span header>Left Icon Position</span>
                    <div content>
                        <p>
                            This is the default accordion type with expandable icons positioned on
                            the left side.
                        </p>
                        <p>
                            It provides a clean, traditional accordion experience with smooth
                            animations.
                        </p>
                    </div>
                </ava-accordion>
            </div>

            <div class="accordion-wrapper">
                <ava-accordion type="default" iconClosed="chevron-down" iconOpen="chevron-up" iconPosition="right"
                    (click)="onAccordionToggle($event)">
                    <span header>Right Icon Position</span>
                    <div content>
                        <p>
                            This accordion has icons positioned on the right side for a different
                            visual layout.
                        </p>
                        <p>
                            Perfect for layouts where you want the icons to be more prominent
                            on the right.
                        </p>
                    </div>
                </ava-accordion>
            </div>

            <div class="accordion-wrapper">
                <ava-accordion type="default" iconClosed="chevron-down" iconOpen="chevron-up" iconPosition="left"
                    [withoutBox]="true" (click)="onAccordionToggle($event)">
                    <span header>Without Box Styling</span>
                    <div content>
                        <p>
                            This accordion variant removes the box styling for a cleaner,
                            borderless appearance.
                        </p>
                        <p>
                            Ideal for minimalist designs or when you want the content to blend
                            seamlessly with the background.
                        </p>
                    </div>
                </ava-accordion>
            </div>

            <div class="section-title">Icon Variants</div>

            <div class="accordion-wrapper">
                <ava-accordion type="titleIcon" titleIcon="settings" iconClosed="chevron-down" iconOpen="chevron-up"
                    (click)="onAccordionToggle($event)">
                    <span header>Settings Panel</span>
                    <div content>
                        <div class="settings-content" [innerHTML]="settingsContent"></div>
                    </div>
                </ava-accordion>
            </div>

            <div class="accordion-wrapper">
                <ava-accordion iconClosed="chevron-right" iconOpen="chevron-down" iconPosition="left">
                    <span header>Traditional Chevron Icons</span>
                    <div content>
                        <p>Traditional chevron icons with left positioning for a classic accordion feel.</p>
                        <p>This pattern is widely recognized and provides clear visual feedback.</p>
                    </div>
                </ava-accordion>
            </div>

            <div class="accordion-wrapper">
                <ava-accordion iconClosed="chevron-right" iconOpen="chevron-down" iconPosition="right">
                    <span header>Right Chevron Icons</span>
                    <div content>
                        <p>Chevron icons positioned on the right side for alternative layouts.</p>
                        <p>Useful when you want to maintain visual consistency with other right-aligned elements.</p>
                    </div>
                </ava-accordion>
            </div>

            <div class="accordion-wrapper">
                <ava-accordion iconClosed="plus" iconOpen="minus" iconPosition="right">
                    <span header>Plus/Minus Icons</span>
                    <div content>
                        <p>
                            Using plus and minus icons for clear expand/collapse indication.
                        </p>
                        <p>
                            These icons are intuitive and provide immediate visual feedback about
                            the accordion's state.
                        </p>
                    </div>
                </ava-accordion>
            </div>

            <div class="section-title">Rich Content Examples</div>

            <div class="accordion-wrapper">
                <ava-accordion iconClosed="chevron-down" iconOpen="chevron-up">
                    <div header>
                        <div style="display: flex; align-items: center; gap: 8px">
                            <span style="font-size: 20px">📊</span>
                            <span>Analytics Dashboard</span>
                        </div>
                    </div>
                    <div content>
                        <div class="dashboard-content">
                            <h5>Dashboard Content</h5>
                            <p>Rich content with custom styling and layout. This demonstrates how accordions can contain
                                complex UI elements.</p>
                            <button class="btn">View Details</button>
                        </div>
                    </div>
                </ava-accordion>
            </div>

            <div class="accordion-wrapper">
                <ava-accordion iconClosed="chevron-down" iconOpen="chevron-up">
                    <div header class="rich-header-content">
                        <h4>Rich Header Content</h4>
                        <small>With subtitle and description</small>
                    </div>
                    <div content>
                        <p>Support for complex header content including:</p>
                        <ul>
                            <li>Multiple text elements</li>
                            <li>Different font sizes</li>
                            <li>Color variations</li>
                            <li>Rich formatting</li>
                            <li>Custom styling</li>
                        </ul>
                        <p>This makes accordions perfect for displaying hierarchical information with context.</p>
                    </div>
                </ava-accordion>
            </div>
        </div>
    </div>
</div>