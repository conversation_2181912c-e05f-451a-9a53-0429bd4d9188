import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DefaultCardComponent } from '../../../../../play-comp-library/src/lib/components/card/default-card/default-card.component';
import { IconComponent } from '../../../../../play-comp-library/src/lib/components/icon/icon.component';
import { AvaTagComponent } from '../../../../../play-comp-library/src/lib/components/tags/tags.component';
import { ButtonComponent } from '../../../../../play-comp-library/src/lib/components/button/button.component';

@Component({
  selector: 'app-test-basic-card',
  imports: [
    CommonModule,
    DefaultCardComponent,
    IconComponent,
    AvaTagComponent,
    ButtonComponent
  ],
  templateUrl: './test-basic-card.component.html',
  styleUrl: './test-basic-card.component.scss'
})
export class TestBasicCardComponent {

}
