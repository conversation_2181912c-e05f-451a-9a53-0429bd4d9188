.cards-container {
    display: flex;
    flex-direction: column;
    gap: 32px;
    margin: 20px;
}

.card-wrapper {
    max-width: 406px;
}

/* First Card - Simple Card Styles */
.simple-card {
    .card-content {
        display: flex;
        flex-direction: column;
        gap: 24px;
    }

    .card-heading {
        color: #3B3F46;
        font-family: Inter;
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px;
        margin: 0;

        /* Truncate at 1st line */
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .card-description {
        overflow: hidden;
        color: #3B3F46;
        text-overflow: ellipsis;
        font-family: Inter;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        margin: 0;

        /* Truncate at 2nd line */
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        white-space: normal;
    }

    .footer-text {
        color: #898E99;
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;

        /* Truncate at 1st line */
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

/* Second Card - Complex Card Styles */
.complex-card {
    .card-content {
        display: flex;
        flex-direction: column;
        gap: 24px;
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .header-left {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;
        min-width: 0;
    }

    .icon-container {
        display: flex;
        width: 36px;
        height: 36px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: 8px;
        background: #E7E7E7;
        flex-shrink: 0;
    }

    .card-title {
        color: #3B3F46;
        font-family: Inter;
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px;
        margin: 0;

        /* Truncate text */
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
        min-width: 0;
    }

    .header-right {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-shrink: 0;
    }

    .rating-text {
        color: #3B3F46;
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 16px;
        letter-spacing: 0.028px;
    }

    .card-description {
        overflow: hidden;
        color: #3B3F46;
        text-overflow: ellipsis;
        font-family: Inter;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        margin: 0;

        /* Truncate after 2 lines */
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        white-space: normal;
    }

    .tags-section {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }

    .meta-info-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .meta-left,
    .meta-right {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .meta-text {
        color: #898E99;
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;

        /* Truncate at 1st line */
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .action-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .action-left {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .action-number {
        color: #898E99;
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
    }

    .action-right {
        display: flex;
        align-items: center;
        gap: 24px;

        .play-button {
            display: flex;
            min-width: 36px;
            padding: 10px 12px;
            justify-content: center;
            align-items: center;
            gap: 8px;
            flex: 1 0 0;
            border-radius: 6px;
            background: linear-gradient(90deg, #E91E63 0%, #D41B5A 100%);
        }
    }
}