<ava-sidebar
  [size]="sidebarConfig.size || 'medium'"
  [width]="sidebarConfig.width || ''"
  [collapsedWidth]="sidebarConfig.collapsedWidth || ''"
  [height]="sidebarConfig.height || '100vh'"
  [showCollapseButton]="sidebarConfig.showCollapseButton ?? true"
  [buttonVariant]="sidebarConfig.buttonVariant || 'inside'"
  [showHeader]="sidebarConfig.showHeader ?? true"
  [showFooter]="sidebarConfig.showFooter ?? false"
  [position]="sidebarConfig.position || 'left'"
  [isCollapsed]="collapsed"
  (collapseToggle)="onCollapseToggle($event)"
  class="custom-sidebar"
  [class.loading]="isLoading"
  [class.has-error]="loadErrorMessage">

  <!-- Header Content -->
  <div slot="header" class="custom-sidebar-header" *ngIf="sidebarConfig.showHeader">
    <ng-content select="[slot=header]">
      <div class="default-header">
        <span class="header-title">Quick Actions</span>
      </div>
    </ng-content>
  </div>

  <!-- Main Content -->
  <div slot="content" class="custom-sidebar-content">
    <!-- Loading State -->
    <div *ngIf="isLoading" class="loading-state">
      <div class="loading-spinner"></div>
      <span class="loading-text" *ngIf="!collapsed">Loading...</span>
    </div>

    <!-- Error State -->
    <div *ngIf="loadErrorMessage && !isLoading" class="error-state">
      <ava-icon iconName="AlertCircle" iconSize="20" iconColor="#ef4444"></ava-icon>
      <span class="error-text" *ngIf="!collapsed">{{ loadErrorMessage }}</span>
    </div>

    <!-- Navigation Content -->
    <div *ngIf="!isLoading && !loadErrorMessage" class="nav-section">
      <ng-content select="[slot=content]">
        <div class="default-nav-section">
          <div *ngFor="let item of sidebarItems"
               class="nav-item"
               [class.active]="item.active"
               [class.disabled]="item.disabled"
               (click)="onItemClick(item)"
               [attr.aria-label]="item.text"
               [attr.aria-disabled]="item.disabled">
            <div class="nav-item-icon">
              <ava-icon
                [iconName]="item.icon"
                iconSize="20"
                [iconColor]="item.active ? '#ffffff' : (item.disabled ? '#9ca3af' : '#6b7280')">
              </ava-icon>
            </div>
            <span class="nav-item-text" *ngIf="!collapsed">{{ item.text }}</span>
          </div>
        </div>
      </ng-content>
    </div>
  </div>

  <!-- Footer Content -->
  <div slot="footer" class="custom-sidebar-footer" *ngIf="sidebarConfig.showFooter">
    <ng-content select="[slot=footer]">
      <div class="default-footer">
        <span class="footer-text" *ngIf="!collapsed">Footer Content</span>
      </div>
    </ng-content>
  </div>
</ava-sidebar>
