.custom-sidebar {
  background: var(--sidebar-background);
  border: var(--sidebar-border);
  border-radius: var(--sidebar-border-radius);
  box-shadow: var(--sidebar-shadow);
  transition: var(--sidebar-transition);

  &.loading {
    opacity: 0.8;
  }

  &.has-error {
    border-color: #ef4444;
  }
}

// Header Styles
.custom-sidebar-header {
  display: flex;
  align-items: center;
  width: 100%;
}

.default-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.header-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

// Content Styles
.custom-sidebar-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

// Loading State
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 2rem 1rem;
  color: #6b7280;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 0.875rem;
  font-weight: 500;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Error State
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 2rem 1rem;
  color: #ef4444;
}

.error-text {
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
}

// Navigation Styles
.nav-section {
  flex: 1;
  overflow-y: auto;
}

.default-nav-section {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;

  &:hover:not(.disabled) {
    background: #f3f4f6;
    color: #374151;
  }

  &.active {
    background: #3b82f6;
    color: white;

    .nav-item-icon {
      background: rgba(255, 255, 255, 0.2);
    }
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.5;
    color: #9ca3af;
  }
}

.nav-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f3f4f6;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.nav-item-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// Footer Styles
.custom-sidebar-footer {
  display: flex;
  align-items: center;
  width: 100%;
}

.default-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.footer-text {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

// Collapsed state adjustments
.custom-sidebar.ava-sidebar.collapsed {
  .nav-item {
    justify-content: center;
    padding: 0.75rem;
    width: 40px;
    height: 40px;
    margin: 0.25rem auto;

    .nav-item-text {
      display: none;
    }
  }

  .loading-state,
  .error-state {
    padding: 1rem 0.5rem;

    .loading-text,
    .error-text {
      display: none;
    }
  }

  .header-title,
  .footer-text {
    display: none;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .custom-sidebar {
    width: 100%;
    max-width: 320px;
  }
}
