import { ChangeDetectionStrategy, Component, ViewEncapsulation, Input, OnInit, inject, ChangeDetectorRef, Output, EventEmitter } from '@angular/core';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { IconComponent } from '../../../public-api';
import { CommonModule } from '@angular/common';
import { Observable, of } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { SidebarComponent, SidebarSize, SidebarPosition, SidebarButtonVariant } from '../../components/sidebar/sidebar.component';

export interface SidebarItem {
  id: string;
  icon: string;
  text: string;
  route?: string;
  active?: boolean;
  disabled?: boolean;
}

export interface CustomSidebarConfig {
  size?: SidebarSize;
  position?: SidebarPosition;
  width?: string;
  collapsedWidth?: string;
  height?: string;
  showCollapseButton?: boolean;
  buttonVariant?: SidebarButtonVariant;
  showHeader?: boolean;
  showFooter?: boolean;
}

@Component({
  selector: 'ava-custom-sidebar',
  imports: [ IconComponent, CommonModule,HttpClientModule,SidebarComponent],
  templateUrl: './custom-sidebar.component.html',
  styleUrl: './custom-sidebar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class CustomSidebarComponent implements OnInit {
  // Data inputs
  @Input() jsonFilePath: string | null = null;
  @Input() sidebarItemsData: SidebarItem[] | null = null;

  // Configuration inputs
  @Input() config: CustomSidebarConfig = {};
  @Input() size: SidebarSize = 'medium';
  @Input() position: SidebarPosition = 'left';
  @Input() width: string = '';
  @Input() collapsedWidth: string = '';
  @Input() height: string = '100vh';
  @Input() showCollapseButton: boolean = true;
  @Input() buttonVariant: SidebarButtonVariant = 'inside';
  @Input() showHeader: boolean = true;
  @Input() showFooter: boolean = false;
  @Input() isCollapsed: boolean = false;

  // Event outputs
  @Output() collapseToggle = new EventEmitter<boolean>();
  @Output() itemClick = new EventEmitter<SidebarItem>();
  @Output() itemsLoaded = new EventEmitter<SidebarItem[]>();
  @Output() loadError = new EventEmitter<string>();

  private http = inject(HttpClient);
  private cdr = inject(ChangeDetectorRef);

  // Internal state
  private _isCollapsed = false;
  sidebarItems: SidebarItem[] = [];
  isLoading = false;
  loadErrorMessage: string | null = null;

  private defaultSidebarItems: SidebarItem[] = [
    {
      id: '1',
      icon: 'home',
      text: 'Dashboard',
      route: '/dashboard',
      active: true,
    },
    { id: '2', icon: 'home', text: 'Users', route: '/users' },
    { id: '3', icon: 'home', text: 'Settings', route: '/settings' },
    { id: '4', icon: 'home', text: 'Analytics', route: '/analytics' },
    { id: '5', icon: 'home', text: 'Messages', route: '/messages' },
    { id: '6', icon: 'home', text: 'Calendar', route: '/calendar' },
  ];

  ngOnInit(): void {
    this._isCollapsed = this.isCollapsed;
    this.initializeSidebarItems();
  }

  private initializeSidebarItems(): void {
    // Priority: sidebarItemsData > jsonFilePath > defaultSidebarItems
    if (this.sidebarItemsData && this.sidebarItemsData.length > 0) {
      this.sidebarItems = this.sidebarItemsData;
      this.itemsLoaded.emit(this.sidebarItems);
    } else if (this.jsonFilePath) {
      this.loadFromJsonFile();
    } else {
      this.sidebarItems = this.defaultSidebarItems;
      this.itemsLoaded.emit(this.sidebarItems);
    }
  }

  get collapsed(): boolean {
    return this._isCollapsed;
  }

  get sidebarConfig() {
    return {
      size: this.config.size || this.size,
      position: this.config.position || this.position,
      width: this.config.width || this.width,
      collapsedWidth: this.config.collapsedWidth || this.collapsedWidth,
      height: this.config.height || this.height,
      showCollapseButton: this.config.showCollapseButton ?? this.showCollapseButton,
      buttonVariant: this.config.buttonVariant || this.buttonVariant,
      showHeader: this.config.showHeader ?? this.showHeader,
      showFooter: this.config.showFooter ?? this.showFooter,
    };
  }

  private loadFromJsonFile(): void {
    if (!this.jsonFilePath) return;

    this.isLoading = true;
    this.loadErrorMessage = null;

    this.loadJsonFile(this.jsonFilePath).subscribe({
      next: (items) => {
        this.sidebarItems = items;
        this.isLoading = false;
        this.itemsLoaded.emit(items);
        this.cdr.detectChanges();
        console.log('Sidebar items loaded from JSON file:', items);
      },
      error: (error) => {
        const errorMessage = `Failed to load sidebar items: ${error}`;
        this.loadErrorMessage = errorMessage;
        this.sidebarItems = this.defaultSidebarItems;
        this.isLoading = false;
        this.loadError.emit(errorMessage);
        this.cdr.detectChanges();
        console.error('Error loading sidebar items from JSON file:', error);
      }
    });
  }

  private loadJsonFile(filePath: string): Observable<SidebarItem[]> {
    return this.http.get<SidebarItem[]>(filePath).pipe(
      tap(items => {
        if (!this.isValidSidebarItems(items)) {
          throw new Error('Invalid sidebar items structure in JSON file');
        }
      }),
      catchError(error => {
        console.error('HTTP Error loading JSON file:', error);
        return of(this.defaultSidebarItems);
      })
    );
  }

  private isValidSidebarItems(items: any): boolean {
    if (!Array.isArray(items)) return false;

    return items.every(item =>
      typeof item === 'object' &&
      item !== null &&
      typeof item.id === 'string' &&
      typeof item.icon === 'string' &&
      typeof item.text === 'string' &&
      (typeof item.route === 'string' || item.route === undefined) &&
      (typeof item.active === 'boolean' || item.active === undefined)
    );
  }

  onItemClick(item: SidebarItem): void {
    if (item.disabled) {
      return;
    }

    // Update active state - set all to false, then set clicked item to true
    this.sidebarItems.forEach(sidebarItem => {
      sidebarItem.active = sidebarItem.id === item.id;
    });

    this.itemClick.emit(item);
    this.cdr.detectChanges();
    console.log('Item clicked:', item);
  }

  onCollapseToggle(isCollapsed: boolean): void {
    this._isCollapsed = isCollapsed;
    this.collapseToggle.emit(isCollapsed);
    console.log('Sidebar collapsed:', isCollapsed);
  }

  // Method to load new JSON file programmatically
  loadNewJsonFile(filePath: string): void {
    this.jsonFilePath = filePath;
    this.loadFromJsonFile();
  }

  // Method to update sidebar items programmatically
  updateSidebarItems(newItems: SidebarItem[]): void {
    this.sidebarItemsData = newItems;
    this.jsonFilePath = null; // Clear file path when setting data directly
    this.sidebarItems = newItems;
  }

  // Method to reload current JSON file
  reloadJsonFile(): void {
    if (this.jsonFilePath) {
      this.loadFromJsonFile();
    }
  }
}
