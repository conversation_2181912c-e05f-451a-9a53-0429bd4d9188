import { ComponentFixture, TestBed } from '@angular/core/testing';
import { LucideAngularModule, Search, SendHorizontal } from 'lucide-angular';

import { SearchBarComponent } from './search-bar.component';

fdescribe('SearchBarComponent', () => {
  let component: SearchBarComponent;
  let fixture: ComponentFixture<SearchBarComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        SearchBarComponent,
        LucideAngularModule.pick({ Search, SendHorizontal })
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(SearchBarComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
