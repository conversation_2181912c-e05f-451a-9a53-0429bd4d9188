import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
  ViewEncapsulation,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ButtonComponent } from '../../components/button/button.component';
import { AvaTextboxComponent } from '../../components/textbox/ava-textbox.component';
import { CheckboxComponent } from '../../components/checkbox/checkbox.component';
import { LinkComponent } from '../../components/link/link.component';
import { DividersComponent } from '../../components/dividers/dividers.component';

export interface SSOLoginCredentials {
  username: string;
  password: string;
  keepSignedIn?: boolean;
}

export interface SSOLoginEvent {
  type: 'login' | 'sso-login' | 'forgot-password' | 'trouble-signin';
  data: unknown;
  credentials?: SSOLoginCredentials;
}

export type SSOLoginVariant = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

@Component({
  selector: 'ava-sso-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ButtonComponent,
    AvaTextboxComponent,
    CheckboxComponent,
    LinkComponent,
    DividersComponent,
  ],
  templateUrl: './sso-login.component.html',
  styleUrl: './sso-login.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class SSOLoginComponent {
  @Input() loading = false;
  @Input() disabled = false;
  @Input() errorMessage = '';

  private _variant: SSOLoginVariant = 'md';
  private _variantExplicitlySet = false;

  @Input()
  set variant(value: SSOLoginVariant) {
    this._variant = value;
    this._variantExplicitlySet = true;
  }
  get variant(): SSOLoginVariant {
    return this._variant;
  }

  @Output() login = new EventEmitter<SSOLoginCredentials>();
  @Output() ssoLogin = new EventEmitter<void>();
  @Output() forgotPassword = new EventEmitter<string>();
  @Output() troubleSignin = new EventEmitter<void>();
  @Output() loginEvent = new EventEmitter<SSOLoginEvent>();

  loginForm: FormGroup;
  showPassword = false;
  submitted = false;

  constructor(private fb: FormBuilder) {
    this.loginForm = this.fb.group({
      username: ['', [Validators.required]],
      password: ['', [Validators.required]],
      keepSignedIn: [false],
    });
  }

  get username() {
    return this.loginForm.get('username');
  }

  get password() {
    return this.loginForm.get('password');
  }

  get keepSignedIn() {
    return this.loginForm.get('keepSignedIn');
  }

  get isFormValid(): boolean {
    return this.loginForm.valid && !this.loading && !this.disabled;
  }

  get usernameError(): string {
    if (this.username?.hasError('required')) {
      return 'Username or email is required';
    }
    return '';
  }

  get passwordError(): string {
    if (this.password?.hasError('required')) {
      return 'Password is required';
    }
    return '';
  }

  onSubmit() {
    this.submitted = true;

    if (this.loginForm.valid) {
      const credentials: SSOLoginCredentials = {
        username: this.username?.value,
        password: this.password?.value,
        keepSignedIn: this.keepSignedIn?.value,
      };

      this.login.emit(credentials);
      this.emitLoginEvent('login', { credentials });
    }
  }

  onSSOLogin() {
    if (!this.loading && !this.disabled) {
      this.ssoLogin.emit();
      this.emitLoginEvent('sso-login', {});
    }
  }

  onForgotPassword() {
    if (!this.loading && !this.disabled) {
      this.forgotPassword.emit(this.username?.value || '');
      this.emitLoginEvent('forgot-password', { username: this.username?.value });
    }
  }

  onTroubleSigningIn() {
    if (!this.loading && !this.disabled) {
      this.troubleSignin.emit();
      this.emitLoginEvent('trouble-signin', {});
    }
  }

  togglePasswordVisibility() {
    this.showPassword = !this.showPassword;
  }

  onUsernameChange(event: Event) {
    const target = event.target as HTMLInputElement;
    this.username?.setValue(target.value);
  }

  onPasswordChange(event: Event) {
    const target = event.target as HTMLInputElement;
    this.password?.setValue(target.value);
  }

  onKeepSignedInChange(event: boolean) {
    this.keepSignedIn?.setValue(event);
  }

  getContainerClasses(): string {
    const classes = ['sso-login-container'];

    // Only apply variant class if variant was explicitly set
    if (this._variantExplicitlySet) {
      classes.push(`sso-login-container--${this.variant}`);
    }

    return classes.join(' ');
  }

  private emitLoginEvent(type: SSOLoginEvent['type'], data: unknown) {
    const event: SSOLoginEvent = {
      type,
      data,
      credentials:
        type === 'login'
          ? (data as { credentials: SSOLoginCredentials }).credentials
          : undefined,
    };
    this.loginEvent.emit(event);
  }

  getTroubleTextClasses(): string {
    return `trouble-text trouble-text--${this.variant}`;
  }
  
  getButtonSize(): 'xsmall' | 'small' | 'medium' | 'large' | 'xlarge' {
    switch (this.variant) {
      case 'xs':
        return 'xsmall';
      case 'sm':
        return 'small';
      case 'md':
        return 'medium';
      case 'lg':
        return 'large';
      case 'xl':
        return 'xlarge';
    }
  }

  getCheckboxSize(): 'small' | 'medium' | 'large' {
    switch (this.variant) {
      case 'xl':
        return 'large';
      case 'lg':
      case 'md':
        return 'medium';
      case 'sm':
      case 'xs':
        return 'small';
    }
  }

  getHyperlinkSize(): 'small' | 'medium' | 'large' {
    switch (this.variant) {
      case 'xl':
      case 'lg':
        return 'small';
      case 'md':
      case 'sm':
      case 'xs':
        return 'small';
    }
  }
}
