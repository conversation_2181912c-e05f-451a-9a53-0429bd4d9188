.sso-login-container {
  display: flex;
  width: 100%;
  padding: var(--global-spacing-5);
  flex-direction: column;
  // align-items: center;
  gap: var(--global-spacing-6);
  border-radius: var(--global-radius-lg);
  background: #FFF;

  // Variant-specific widths
  &.sso-login-container--xs {
    width: 500px;
  }

  &.sso-login-container--sm {
    width: 500px;
  }

  &.sso-login-container--md {
    width: 500px;
  }

  &.sso-login-container--lg {
    width: 500px;
  }

  &.sso-login-container--xl {
    width: 560px;
  }

  .login-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--global-spacing-3, 12px);
    align-self: stretch;

    .login-title {
      color: var(--color-text-primary);

      /* Desktop/Heading/H4 */
      font-family: var(--font-family-heading);
      font-size: var(--global-font-size-xl);
      font-style: normal;
      font-weight: var(--global-font-weight-bold);
      line-height: var(--global-line-height-loose);
      /* 116.667% */
    }

    .login-subtitle {
      color: #000;
      text-align: center;

      /* Tablet/Body/Body 2 Regular */
      font-family: var(--font-family-body);
      font-size: var(--global-font-size-md);
      font-style: normal;
      font-weight: var(--global-font-weight-regular);
      line-height: var(--global-line-height-tight);
      /* 125% */
    }
  }

  .login-form {
    display: flex;
    flex-direction: column;
    // align-items: flex-start;
    gap: var(--Global-v1-Size-24, 24px);
    align-self: stretch;

    .inputs-field {}



    .form-options {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
  }

  .divider-section {
    display: flex;
    align-items: flex-end;
    gap: 36px;
    align-self: stretch;

    .divider-text {
      color: #000;
      text-align: center;

      /* Body 2 | Reg */
      font-family: var(--font-family-body);
      font-size: var(--global-font-size-sm);
      font-style: normal;
      font-weight: var(--global-font-weight-regular);
      line-height: 22px;
      /* 157.143% */
    }

    ava-dividers {
      flex: 1;
    }
  }

  .trouble-signin {
    .trouble-text {
      color: #000;
      font-family: var(--font-family-body);
      font-weight: var(--global-font-weight-regular);
      &--xs {
        font-size: var(--global-font-size-xs);
      }

      &--sm {
        font-size: var(--global-font-size-xs);
      }

      &--md {
        font-size: var(--global-font-size-sm);
      }

      &--lg {
        font-size: var(--global-font-size-sm);
      }

      &--xl {
        font-size: var(--global-font-size-sm);
      }
    }

    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--global-spacing-1);
    align-self: stretch;
  }
}