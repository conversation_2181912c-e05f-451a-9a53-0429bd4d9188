// image-card.component.spec.ts
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ImageCardComponent } from './image-card.component';
import { By } from '@angular/platform-browser';
import { CommonModule } from '@angular/common';

describe('ImageCardComponent', () => {
  let component: ImageCardComponent;
  let fixture: ComponentFixture<ImageCardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ImageCardComponent, CommonModule]
    }).compileComponents();

    fixture = TestBed.createComponent(ImageCardComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should return true for vertical layout', () => {
    component.data.layout.orientation = 'vertical';
    expect(component.isVertical).toBe(true);
    expect(component.isHorizontal).toBe(false);
  });

  it('should return true for horizontal layout', () => {
    component.data.layout.orientation = 'horizontal';
    expect(component.isHorizontal).toBe(true);
    expect(component.isVertical).toBe(false);
  });

  it('should return true for withActions variant', () => {
    component.data.variant = 'withActions';
    expect(component.isWithActions).toBe(true);
    expect(component.isWithoutActions).toBe(false);
  });

  it('should return true for withoutActions variant', () => {
    component.data.variant = 'withoutActions';
    expect(component.isWithoutActions).toBe(true);
    expect(component.isWithActions).toBe(false);
  });

  it('should calculate correct maxWidth for vertical withActions', () => {
    component.data.variant = 'withActions';
    component.data.layout.orientation = 'vertical';
    expect(component.maxWidth).toBe('320px');
  });

  it('should calculate correct maxWidth for horizontal withActions', () => {
    component.data.variant = 'withActions';
    component.data.layout.orientation = 'horizontal';
    expect(component.maxWidth).toBe('487px');
  });

  it('should emit cardClick when card is clicked', () => {
    spyOn(component.cardClick, 'emit');
    component.onCardClick();
    expect(component.cardClick.emit).toHaveBeenCalled();
  });

  it('should emit buttonClicked when button is clicked', () => {
    const mockButton = { text: 'Test', action: 'test-action' };
    spyOn(component.buttonClicked, 'emit');
    component.onButtonClick('test-action', mockButton);
    expect(component.buttonClicked.emit).toHaveBeenCalledWith({ action: 'test-action', button: mockButton });
  });

  it('should render title from data', () => {
    component.data.title = 'Test Title';
    fixture.detectChanges();

    const titleElement = fixture.debugElement.query(By.css('.title'));
    expect(titleElement.nativeElement.textContent.trim()).toBe('Test Title');
  });

  it('should render image from data', () => {
    component.data.image = 'test.jpg';
    component.data.title = 'Test Alt';
    fixture.detectChanges();

    const imgElement = fixture.debugElement.query(By.css('img'));
    expect(imgElement.nativeElement.src).toContain('test.jpg');
    expect(imgElement.nativeElement.alt).toBe('Test Alt');
  });
});
