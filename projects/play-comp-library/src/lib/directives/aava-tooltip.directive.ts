import {
  Directive,
  ElementRef,
  HostListener,
  Input,
  ComponentRef,
  ViewContainerRef,
  ComponentFactoryResolver,
  Injector,
  OnDestroy,
  OnInit
} from '@angular/core';
import { AavaTooltipComponent } from '../components/tooltip/aava-tooltip.component';

@Directive({
  selector: '[aavaTooltipTitle], [aavaTooltipDescription]'
})
export class AavaTooltipDirective implements OnInit {

  @Input() aavaTooltipTitle: string = '';
  @Input() aavaTooltipDescription: string = '';
  @Input() aavaTooltipType: 'simple' | 'card' | 'guided' = 'simple';
  @Input() aavaTooltipArrow: 'start' | 'center' | 'end' | null = null;
  @Input() aavaTooltipTrigger: 'hover' | 'click' | 'focus' = 'hover';
  @Input() aavaTooltipPosition: 'top' | 'bottom' | 'left' | 'right' = 'top';
  @Input() aavaTooltipSize: 'small' | 'medium' | 'large' = 'medium';
  @Input() aavaTooltipVariant: 'default' = 'default';
  @Input() aavaTooltipIcon: string = '';
  @Input() aavaTooltipIconColor: string = '';

  private tooltipRef: ComponentRef<AavaTooltipComponent> | null = null;

  tooltipElement!: HTMLElement;

  constructor(
    private vcRef: ViewContainerRef,
    private resolver: ComponentFactoryResolver,
    private injector: Injector,
    private el: ElementRef
  ) { }

  ngOnInit() {
    // Directive initialized
  }

  @HostListener('mouseenter') onMouseEnter() {
    if (this.aavaTooltipTrigger === 'hover') this.show();
  }

  @HostListener('mouseleave') onMouseLeave() {
    if (this.aavaTooltipTrigger === 'hover') this.hide();
  }

  @HostListener('focus') onFocus() {
    if (this.aavaTooltipTrigger === 'focus') this.show();
  }

  @HostListener('blur') onBlur() {
    if (this.aavaTooltipTrigger === 'focus') this.hide();
  }

  @HostListener('click') onClick() {
    if (this.aavaTooltipTrigger === 'click') {
      this.tooltipRef ? this.hide() : this.show();
    }
  }

  show() {
    if (this.tooltipRef) return;
    const rect = this.el.nativeElement.getBoundingClientRect();
    const scrollY = window.scrollY;
    const scrollX = window.scrollX;
    const factory = this.resolver.resolveComponentFactory(AavaTooltipComponent);
    this.tooltipRef = this.vcRef.createComponent(factory);

    // Calculate absolute positions (viewport position + scroll offset)
    const absoluteLeft = rect.left + scrollX;
    const absoluteTop = rect.top + scrollY;
    const absoluteBottom = rect.bottom + scrollY;

    // Create tooltip configuration
    this.tooltipRef.instance.config = {
      title: this.aavaTooltipTitle,
      description: this.aavaTooltipDescription,
      type: this.aavaTooltipType,
      arrow: this.aavaTooltipArrow,
      size: this.aavaTooltipSize,
      position: this.aavaTooltipPosition,
      variant: this.aavaTooltipVariant,
      icon: this.aavaTooltipIcon,
      iconColor: this.aavaTooltipIconColor,
      left: absoluteLeft,
      top: absoluteTop,
      bottom: absoluteBottom,
      width: rect.width,
      height: rect.height,
    };
  }

  hide() {
    if (this.tooltipRef) {
      this.tooltipRef.destroy();
      this.tooltipRef = null;
    }
  }

  ngOnDestroy() {
    this.hide();
  }

}

 