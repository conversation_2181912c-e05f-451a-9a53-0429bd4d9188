import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { OtpComponent } from './otp.component';

describe('OtpComponent', () => {
  let component: OtpComponent;
  let fixture: ComponentFixture<OtpComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [OtpComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(OtpComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.length).toBe(6);
    expect(component.variant).toBe('default');
    expect(component.size).toBe('medium');
    expect(component.disabled).toBe(false);
    expect(component.readonly).toBe(false);
    expect(component.mask).toBe(false);
  });

  it('should initialize OTP values array with correct length', () => {
    component.length = 4;
    component.ngAfterViewInit();
    expect(component.otpValues).toEqual(['', '', '', '']);
  });

  it('should generate correct number of OTP boxes', () => {
    component.length = 4;
    expect(component.otpBoxes).toEqual([0, 1, 2, 3]);
  });

  it('should apply correct CSS classes', () => {
    component.size = 'lg';
    component.variant = 'success';
    component.disabled = true;

    const expectedClasses = 'ava-otp ava-otp--lg ava-otp--success ava-otp--disabled';
    expect(component.wrapperClasses).toBe(expectedClasses);
  });

  it('should handle OTP input correctly', () => {
    const mockEvent = {
      target: { value: '5' }
    } as any;

    spyOn(component.change, 'emit');
    component.onOtpInput(mockEvent, 2);

    expect(component.otpValues[2]).toBe('5');
    expect(component.change.emit).toHaveBeenCalledWith('  5   ');
  });

  it('should emit complete event when OTP is fully entered', () => {
    spyOn(component.complete, 'emit');

    // Fill all OTP inputs
    for (let i = 0; i < component.length; i++) {
      const mockEvent = {
        target: { value: (i + 1).toString() }
      } as any;
      component.onOtpInput(mockEvent, i);
    }

    expect(component.complete.emit).toHaveBeenCalledWith('123456');
  });

  it('should handle backspace correctly', () => {
    // Set some values first
    component.otpValues = ['1', '2', '3', '', '', ''];

    const mockEvent = {
      key: 'Backspace',
      target: { value: '' }
    } as any;

    spyOn(component.change, 'emit');
    component.onOtpKeydown(mockEvent, 2);

    expect(component.otpValues[2]).toBe('');
    expect(component.change.emit).toHaveBeenCalled();
  });

  it('should handle paste correctly', () => {
    const mockEvent = {
      preventDefault: jasmine.createSpy('preventDefault'),
      clipboardData: {
        getData: jasmine.createSpy('getData').and.returnValue('123456')
      }
    } as any;

    spyOn(component.change, 'emit');
    spyOn(component.complete, 'emit');

    component.onOtpPaste(mockEvent, 0);

    expect(mockEvent.preventDefault).toHaveBeenCalled();
    expect(component.otpValues).toEqual(['1', '2', '3', '4', '5', '6']);
    expect(component.complete.emit).toHaveBeenCalledWith('123456');
  });

  it('should clear all values when clear() is called', () => {
    // Set some values first
    component.otpValues = ['1', '2', '3', '4', '5', '6'];

    spyOn(component.change, 'emit');
    component.clear();

    expect(component.otpValues).toEqual(['', '', '', '', '', '']);
    expect(component.change.emit).toHaveBeenCalledWith('');
  });

  it('should show error message when error is set', () => {
    component.error = 'Invalid OTP';
    fixture.detectChanges();

    const errorElement = fixture.debugElement.query(By.css('.ava-otp__error'));
    expect(errorElement).toBeTruthy();
    expect(errorElement.nativeElement.textContent.trim()).toContain('Invalid OTP');
  });

  it('should show helper message when helper is set and no error', () => {
    component.helper = 'Enter the 6-digit code';
    fixture.detectChanges();

    const helperElement = fixture.debugElement.query(By.css('.ava-otp__helper'));
    expect(helperElement).toBeTruthy();
    expect(helperElement.nativeElement.textContent.trim()).toContain('Enter the 6-digit code');
  });

  it('should not show helper when error is present', () => {
    component.error = 'Invalid OTP';
    component.helper = 'Enter the 6-digit code';
    fixture.detectChanges();

    const helperElement = fixture.debugElement.query(By.css('.ava-otp__helper'));
    expect(helperElement).toBeFalsy();
  });

  it('should implement ControlValueAccessor correctly', () => {
    const testValue = '123456';

    component.writeValue(testValue);
    expect(component.otpValues).toEqual(['1', '2', '3', '4', '5', '6']);

    const onChangeSpy = jasmine.createSpy('onChange');
    component.registerOnChange(onChangeSpy);

    const mockEvent = {
      target: { value: '7' }
    } as any;
    component.onOtpInput(mockEvent, 0);

    expect(onChangeSpy).toHaveBeenCalled();
  });

  it('should handle disabled state correctly', () => {
    component.setDisabledState(true);
    expect(component.disabled).toBe(true);

    // Should not process input when disabled
    const mockEvent = {
      target: { value: '5' }
    } as any;

    const originalValue = component.otpValues[0];
    component.onOtpInput(mockEvent, 0);
    expect(component.otpValues[0]).toBe(originalValue);
  });

  it('should render correct number of input boxes', () => {
    component.length = 4;
    fixture.detectChanges();

    const inputElements = fixture.debugElement.queryAll(By.css('.ava-otp__input'));
    expect(inputElements.length).toBe(4);
  });

  it('should apply mask type when mask is true', () => {
    component.mask = true;
    fixture.detectChanges();

    const inputElements = fixture.debugElement.queryAll(By.css('.ava-otp__input'));
    inputElements.forEach(input => {
      expect(input.nativeElement.type).toBe('password');
    });
  });
});
