import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SpinnerComponent } from './spinner.component';
import { By } from '@angular/platform-browser';

describe('SpinnerComponent', () => {
  let component: SpinnerComponent;
  let fixture: ComponentFixture<SpinnerComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SpinnerComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(SpinnerComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have correct size property', () => {
    component.size = 'lg';
    expect(component.size).toBe('lg');
  });

  it('should return correct sizePx for different sizes', () => {
    component.size = 'sm';
    expect(component.sizePx).toBe(14.62);

    component.size = 'lg';
    expect(component.sizePx).toBe(35.088);
  });

  it('should return correct colors for different color inputs', () => {
    component.color = 'primary';
    expect(component.colors).toBe('var(--spinner-primary-fill)');

    component.color = 'success';
    expect(component.colors).toBe('var(--spinner-success-fill)');

    component.color = '#1681FF';
    expect(component.colors).toBe('#1681FF');
  });

  it('should handle progressIndex correctly', () => {
    component.progressIndex = 25;
    expect(component.progressIndex).toBe(25);

    component.progressIndex = 75;
    expect(component.progressIndex).toBe(75);
  });

  it('should return spinning state based on animation', () => {
    component.animation = true;
    expect(component.spinning).toBe(true);

    component.animation = false;
    expect(component.spinning).toBe(false);
  });

  it('should render spinner with correct attributes', () => {
    component.type = 'circular';
    component.animation = true;
    component.color = 'success';
    component.size = 'md';
    fixture.detectChanges();

    const svg = fixture.debugElement.query(By.css('svg'));
    expect(svg).toBeTruthy();
    expect(svg.nativeElement.getAttribute('width')).toBe('17.544');
    expect(svg.nativeElement.getAttribute('height')).toBe('17.544');
  });

  it('should generate unique gradient ID', () => {
    expect(component.gradientId).toContain('ava_spinner_gradient_');
  });

  it('should have correct default values', () => {
    expect(component.color).toBe('primary');
    expect(component.size).toBe('md');
    expect(component.animation).toBe(false);
    expect(component.type).toBe('circular');
    expect(component.progressIndex).toBe(25);
  });
});
