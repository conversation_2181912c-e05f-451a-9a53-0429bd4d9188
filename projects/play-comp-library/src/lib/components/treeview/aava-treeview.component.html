<ul class="tree" [ngClass]="[size, 'icon-' + iconPosition]" *ngIf="nodes?.length" role="tree"
    [attr.aria-label]="'Tree View'">
    <ng-container *ngFor="let node of nodes">
        <li [style.padding-left.px]="calculateIndent(node.level)" role="treeitem"
            [attr.aria-expanded]="node.children?.length ? !!node.expanded : null"
            [attr.aria-selected]="!!node.selected">
            <div class="tree-node" [class.selected]="node.selected" (click)="selectNode(node)" [ngClass]="iconPosition"
                tabindex="0" (keydown.enter)="selectNode(node)" (keydown)="handleKeyDown($event, node)">
                <ng-container *ngIf="iconPosition === 'left'">
                    <span class="toggle" *ngIf="node.children?.length"
                        (click)="toggleExpand(node); $event.stopPropagation()" role="button"
                        [attr.aria-label]="node.expanded ? 'Collapse ' + node.name : 'Expand ' + node.name"
                        tabindex="-1">
                        <lucide-angular [name]="node.expanded ? 'chevron-down' : 'chevron-right'"></lucide-angular>
                    </span>

                    <lucide-angular *ngIf="node.icon"
                        [name]="node.expanded && node.icon === 'folder' ? 'folder-open' : node.icon"
                        [attr.aria-hidden]="true">
                    </lucide-angular>

                    <span class="label">{{ node.name }}</span>
                </ng-container>

                <ng-container *ngIf="iconPosition === 'right'">
                    <lucide-angular *ngIf="node.icon"
                        [name]="node.expanded && node.icon === 'folder' ? 'folder-open' : node.icon"
                        [attr.aria-hidden]="true">
                    </lucide-angular>

                    <span class="label">{{ node.name }}</span>

                    <span class="toggle" *ngIf="node.children?.length"
                        (click)="toggleExpand(node); $event.stopPropagation()" role="button"
                        [attr.aria-label]="node.expanded ? 'Collapse ' + node.name : 'Expand ' + node.name"
                        tabindex="-1">
                        <lucide-angular [name]="node.expanded ? 'chevron-down' : 'chevron-right'"></lucide-angular>
                    </span>
                </ng-container>
            </div>

            <aava-treeview *ngIf="node.expanded && node.children?.length" [nodes]="node.children || []" [size]="size"
                [iconPosition]="iconPosition" [level]="(node.level ?? 0) + 1" (nodeSelect)="nodeSelect.emit($event)">
            </aava-treeview>
        </li>
    </ng-container>
</ul>