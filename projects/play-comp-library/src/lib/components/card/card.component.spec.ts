import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CardComponent } from './card.component';
import { By } from '@angular/platform-browser';

describe('CardComponent', () => {
  let component: CardComponent;
  let fixture: ComponentFixture<CardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CardComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(CardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should render card element', () => {
    const cardElement = fixture.debugElement.query(By.css('.ava-card'));
    expect(cardElement).toBeTruthy();
  });

  it('should have correct role attribute', () => {
    const cardContainer = fixture.debugElement.query(By.css('.ava-card-container'));
    expect(cardContainer.nativeElement.getAttribute('role')).toBe('listitem');
  });

  it('should render projected header content', () => {
    // This test would need to be done with a host component that projects content
    const headerElement = fixture.debugElement.query(By.css('.card-header'));
    expect(headerElement).toBeTruthy();
  });

  it('should render projected content', () => {
    const contentElement = fixture.debugElement.query(By.css('.card-content'));
    expect(contentElement).toBeTruthy();
  });

  it('should render projected footer content', () => {
    const footerElement = fixture.debugElement.query(By.css('.card-footer'));
    expect(footerElement).toBeTruthy();
  });

  it('should have correct heading property', () => {
    component.heading = 'Test Heading';
    expect(component.heading).toBe('Test Heading');
  });

  it('should have correct content property', () => {
    component.content = 'Test Content';
    expect(component.content).toBe('Test Content');
  });

  it('should have default empty values', () => {
    expect(component.heading).toBe('');
    expect(component.content).toBe('');
  });

  it('should apply hover transform on card', () => {
    const cardElement = fixture.debugElement.query(By.css('.ava-card'));
    expect(cardElement.nativeElement).toBeTruthy();
    // CSS hover effects would need integration testing
  });
});
