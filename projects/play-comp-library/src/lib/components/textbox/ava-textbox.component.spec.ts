import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AvaTextboxComponent } from './ava-textbox.component';

describe('AvaTextboxComponent', () => {
  let component: AvaTextboxComponent;
  let fixture: ComponentFixture<AvaTextboxComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AvaTextboxComponent]
    })
      .compileComponents();

    fixture = TestBed.createComponent(AvaTextboxComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Basic functionality', () => {
    it('should initialize OTP values array', () => {
      expect(component.otpValues).toEqual([]);
    });

    it('should handle input correctly', () => {
      const mockEvent = {
        target: { value: 'test value' }
      } as any;

      component.onInput(mockEvent);
      expect(component.value).toBe('test value');
    });

    it('should distribute value correctly on writeValue', () => {
      component.writeValue('test123');
      expect(component.value).toBe('test123');
    });

    it('should handle partial values correctly', () => {
      component.writeValue('abc');
      expect(component.value).toBe('abc');
    });
  });
});
