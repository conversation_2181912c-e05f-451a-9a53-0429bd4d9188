import { ChangeDetectionStrategy, Component, Input, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../icon/icon.component';
 
export type BadgeState = 'high-priority' | 'medium-priority' | 'low-priority' | 'neutral' | 'information';
export type BadgeSize = 'lg' | 'md' | 'sm' | 'xs';
export type BadgeVariant = 'default' | 'dots';
 
@Component({
  selector: 'ava-badges',
  imports: [CommonModule, IconComponent],
  templateUrl: './badges.component.html',
  styleUrls: ['./badges.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BadgesComponent {
  @Input() state: BadgeState = 'neutral';
  @Input() size: BadgeSize = 'md';
  @Input() variant: BadgeVariant = 'default';
  @Input() count?: number;
  @Input() iconName?: string;
  @Input() iconColor?: string = 'white';
  @Input() iconSize?: number;
 
  get displayCount(): string {
    if (!this.count) return '';
    if (this.count > 99) return '99+';
    if (this.count > 9) return '9+';
    return this.count.toString();
  }
 
  get badgeClasses(): string {
    const baseClasses = `badge badge--${this.state} badge--${this.size} badge--${this.variant}`;
    // Add expanded class only for multi-character content
    if (this.count && this.displayCount.length > 1) {
      return `${baseClasses} badge--expanded`;
    }
    return baseClasses;
  }
 
  get hasContent(): boolean {
    return !!(this.count || this.iconName) && this.variant !== 'dots';
  }
 
  get isDots(): boolean {
    return this.variant === 'dots';
  }
 
  get isSingleDigit(): boolean {
    return this.count !== undefined && this.count >= 0 && this.count <= 9;
  }
 
  onKeyPress() {
    // Handle the key press event, e.g., trigger an action or navigate
    console.log('Badge component pressed via keyboard');
    // Add your custom logic here
  }
}
 
 