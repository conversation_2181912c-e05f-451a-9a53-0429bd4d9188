.user-table-wrapper {
  table-layout: auto;

  .table-wrapper {
    position: relative;
    overflow-x: auto;
    width: 100%;

    .ava-user-table {
      table-layout: auto;
      width: max-content;
      min-width: 100%;
      border-collapse: collapse;
      font-family: var(--table-font-family-body);
      margin: 1rem 0;
      border: 1px solid var(--table-border);

      th,
      td {
        padding: 1rem;
        text-align: left;
      }

      thead {
        background-color: var(--table-header-background-color);
      }

      tbody tr {
        transition: background-color 0.3s ease;

        &:nth-child(odd) {
          background-color: var(--table-background-color-odd);
        }

        &:nth-child(even) {
          background: var(--table-background-color-even);
        }

        .cell-link {
          color: inherit;
          text-decoration: none;
          cursor: pointer;

          &:hover {
            text-decoration: underline;
          }
        }
      }

      .ava-icon {
        margin: 2px;
        padding: 2px;
      }
    }

    .dropdown {
      position: relative;

      [data-theme="dark"] .action-button {
        color: var(--table-background-color-odd);

        &:hover {
          color: var(--table-background-color-odd);
        }
      }

      .dropdown-menu {
        position: absolute;
        top: 100%;
        right: 0;
        margin-top: 0.5rem;
        min-width: 13.75rem;
        padding: 0.25rem 0;
        background: var(--table-background-color-odd);
        border: 1px solid var(--table-border);
        border-radius: 0.5rem;
        box-shadow: 0 0.25rem 0.625rem rgba(0, 0, 0, 0.08);
        z-index: 1000;

        .dropdown-item {
          display: flex;
          align-items: center;
          gap: 0.625rem;
          width: 100%;
          padding: 0.625rem 1rem;
          font-size: 1rem;
          text-align: left;
          background: none;
          border: none;
          cursor: pointer;
          white-space: nowrap;
          color: var(--text-color);

          &:hover {
            background: var(--table-background-color-odd);
          }

          .dropdown-icon {
            font-size: 1rem;
          }
        }
      }
    }

    .actions-cell {
      width: 1%;
      white-space: nowrap;
      text-align: right;

      .action-button {
        background: none;
        border: none;
        font-size: 1.25rem;
        cursor: pointer;
        padding: 0;

        &:hover {
          background-color: rgba(0, 0, 0, 0.05);
        }
      }
    }

    .actions-wrapper {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      position: relative;

      .action-button,
      .action-icon {
        background: none;
        border: none;
        font-size: 1.125rem;
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 0.375rem;

        &:hover {
          background-color: rgba(0, 0, 0, 0.05);
        }
      }
    }

    th {
      position: relative;

      .th-content {
        display: inline-flex;
        align-items: center;
        gap: 4px;
      }

      .filter-icon {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        z-index: 1;

        &.active {
          color: #446dd4;
        }
      }
    }
  }
}

.default-filter-modal {
  position: absolute;
  z-index: 999;
  background-color: var(--table-background-color-odd);
  border: 1px solid var(--table-border);
  border-radius: 8px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  min-width: 240px;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  .default-filter-select,
  .default-filter-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border: 1px solid var(--table-border);
    border-radius: 4px;
  }

  .default-filter-actions {
    display: flex;
    justify-content: space-between;
    gap: 0.5rem;

    button {
      flex: 1;
      padding: 0.5rem 0;
      font-size: 0.875rem;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
  }

  .checkbox-header,
  .checkbox-cell {
    width: 40px;
    text-align: center;
  }
}
