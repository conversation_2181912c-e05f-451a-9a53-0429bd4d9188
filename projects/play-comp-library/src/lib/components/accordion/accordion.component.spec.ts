import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AccordionComponent } from './accordion.component';
import { By } from '@angular/platform-browser';

describe('AccordionComponent', () => {
    let component: AccordionComponent;
    let fixture: ComponentFixture<AccordionComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [AccordionComponent]
        }).compileComponents();

        fixture = TestBed.createComponent(AccordionComponent);
        component = fixture.componentInstance;
    });

    it('should create the component', () => {
        expect(component).toBeTruthy();
    });

    it('should return correct accordionClasses', () => {
        component.animation = true;
        component.expanded = true;
        component.withoutBox = false;
        component.size = 'large';
        expect(component.accordionClasses).toEqual({
            animated: true,
            expanded: true,
            'without-box': false,
            'size-large': true,
        });
    });

    it('should toggle expanded state if not controlled', () => {
        component.expanded = false;
        component.controlled = false;
        component.toggleExpand();
        expect(component.expanded).toBeTrue();
    });

    it('should not toggle expanded state if controlled', () => {
        component.expanded = false;
        component.controlled = true;
        component.toggleExpand();
        expect(component.expanded).toBeFalse();
    });

    it('should toggle expand on Enter key press', () => {
        component.expanded = false;
        component.controlled = false;
        const event = new KeyboardEvent('keydown', { key: 'Enter' });
        spyOn(event, 'preventDefault');
        component.onAccordionKeydown(event);
        expect(component.expanded).toBeTrue();
        expect(event.preventDefault).toHaveBeenCalled();
    });

    it('should toggle expand on Space key press', () => {
        component.expanded = false;
        component.controlled = false;
        const event = new KeyboardEvent('keydown', { key: ' ' });
        spyOn(event, 'preventDefault');
        component.onAccordionKeydown(event);
        expect(component.expanded).toBeTrue();
        expect(event.preventDefault).toHaveBeenCalled();
    });

    it('should not toggle on other key press', () => {
        component.expanded = false;
        component.controlled = false;
        const event = new KeyboardEvent('keydown', { key: 'Escape' });
        spyOn(event, 'preventDefault');
        component.onAccordionKeydown(event);
        expect(component.expanded).toBeFalse();
        expect(event.preventDefault).not.toHaveBeenCalled();
    });
});
