import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input, ViewChild, ElementRef, AfterViewInit, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { LucideAngularModule } from 'lucide-angular';
import { IconComponent } from '../icon/icon.component';

@Component({
  selector: 'ava-accordion',
  imports: [CommonModule, IconComponent],
  standalone: true,
  templateUrl: './accordion.component.html',
  styleUrl: './accordion.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AccordionComponent implements AfterViewInit, OnInit, OnChanges {
  @Input() expanded = false;
  @Input() size : 'sm' | 'md' | 'lg' = 'lg';
  @Input() animation = true;
  @Input() controlled = false;
  @Input() iconClosed = ''; 
  @Input() iconOpen = '';  
  @Input() titleIcon = '';  
  @Input() iconPosition: 'left' | 'right' = 'left';
  @Input() type: 'default' | 'titleIcon' = 'default';
  @Input() withoutBox: boolean = false;

  contentHeight = 0;
  @ViewChild('bodyRef') bodyRef!: ElementRef;
  // Divider visibility flags decoupled from expanded to allow smooth closing behavior
  showHeaderDivider = true;
  showContentDivider = false;
  private isClosing = false;
  iconSize = 20;

  
  get accordionClasses() {
    return {
      animated: this.animation,
      expanded: this.expanded,
      'without-box' : this.withoutBox,
      [`size-${this.size}`]: true, // Add size class
    };
  }

  get iconColor(): string {
    return 'var(--accordion-dark-header-background)';
  }

  // Get icon size based on size property
  get currentIconSize(): number {
    switch (this.size) {
      case 'sm': return 16;
      case 'md': return 20;
      case 'lg': return 20;
      default: return 20;
    }
  }

  get currentFontSize(): string {
    switch (this.size) {
      case 'sm': return '100';
      case 'md': return '200';
      case 'lg': return '300';
      default: return '300';
    }
  }

  ngAfterViewInit() {
    // Capture content height for smooth animation
    if (this.bodyRef && this.bodyRef.nativeElement) {
      const content = this.bodyRef.nativeElement.querySelector('.accordion-content');
      if (content) {
        this.contentHeight = content.scrollHeight;
      }
    }
  }

  ngOnInit() {
    this.showHeaderDivider = !this.expanded;
    this.showContentDivider = this.expanded;
    // Set icon size based on size property
    this.iconSize = this.currentIconSize;
  }

  ngOnChanges(changes: SimpleChanges) {
    if ('expanded' in changes && !changes['expanded'].firstChange) {
      const prev = changes['expanded'].previousValue;
      const curr = changes['expanded'].currentValue;
      if (prev === true && curr === false) {
        this.isClosing = true;
        this.showHeaderDivider = false;
        this.showContentDivider = true;
      } else if (prev === false && curr === true) {
        this.isClosing = false;
        this.showHeaderDivider = false;
        this.showContentDivider = true;
      }
    }

    // Update icon size when size property changes
    if ('size' in changes) {
      this.iconSize = this.currentIconSize;
    }
  }

  onAccordionKeydown(event: KeyboardEvent) {
    if (event.key === 'Enter' || event.key === ' ') {
      this.toggleExpand();
      event.preventDefault();
    }
  }

  toggleExpand() {
    if (this.controlled) {
      return;
    }
    if (this.expanded) {
      // Start closing: keep content divider visible until transition ends
      this.isClosing = true;
      this.showHeaderDivider = false;
      this.showContentDivider = true;
      this.expanded = false;
    } else {
      // Opening: show content divider immediately
      this.isClosing = false;
      this.showHeaderDivider = false;
      this.showContentDivider = true;
      this.expanded = true;
    }
  }

  onBodyTransitionEnd(event: TransitionEvent) {
    // Only act on height transition of the accordion body container
    if (event.propertyName !== 'height') {
      return;
    }
    if (this.isClosing && !this.expanded) {
      // Closing animation finished; switch divider to header
      this.showContentDivider = false;
      this.showHeaderDivider = true;
      this.isClosing = false;
    }
  }
}