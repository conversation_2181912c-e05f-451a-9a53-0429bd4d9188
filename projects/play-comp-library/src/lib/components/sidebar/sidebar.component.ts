import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, ViewEncapsulation } from '@angular/core';
import { ButtonComponent } from '../button/button.component';

// Type definitions for better type safety
export type SidebarSize = 'small' | 'medium' | 'large';
export type SidebarPosition = 'left' | 'right';
export type SidebarButtonVariant = 'inside' | 'outside';

export interface SidebarConfig {
  size?: SidebarSize;
  position?: SidebarPosition;
  width?: string;
  collapsedWidth?: string;
  height?: string;
  showCollapseButton?: boolean;
  buttonVariant?: SidebarButtonVariant;
  showHeader?: boolean;
  showFooter?: boolean;
  isCollapsed?: boolean;
  hoverAreaWidth?: string;
}

@Component({
  selector: 'ava-sidebar',
  imports: [CommonModule, ButtonComponent],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  host: {
    '[attr.data-size]': 'size',
    '[attr.data-position]': 'position',
    '[attr.data-collapsed]': 'collapsed',
    '[attr.aria-expanded]': '!collapsed',
    'role': 'navigation'
  }
})
export class SidebarComponent implements OnInit, OnChanges {
  @Input() size: SidebarSize = 'medium';
  @Input() width: string = '';
  @Input() collapsedWidth: string = '';
  @Input() height: string = '100vh';
  @Input() hoverAreaWidth: string = '10px';
  @Input() showCollapseButton: boolean = false;
  @Input() buttonVariant: SidebarButtonVariant = 'inside';
  @Input() showHeader: boolean = true;
  @Input() showFooter: boolean = true;
  @Input() isCollapsed: boolean = false;
  @Input() position: SidebarPosition = 'left';

  @Output() collapseToggle = new EventEmitter<boolean>();

  private _isCollapsed = false;

  constructor(private cdr: ChangeDetectorRef) { }

  ngOnInit() {
    this._isCollapsed = this.isCollapsed;
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['isCollapsed'] && !changes['isCollapsed'].firstChange) {
      this._isCollapsed = this.isCollapsed;
      this.cdr.markForCheck();
    }
  }

  toggleCollapse(): void {
    this._isCollapsed = !this._isCollapsed;
    this.collapseToggle.emit(this._isCollapsed);
    this.cdr.markForCheck();
  }

  get sidebarWidth(): string {
    if (this._isCollapsed) {
      return this.collapsedWidth || this.getDefaultCollapsedWidth();
    }
    return this.width || this.getDefaultWidth();
  }

  get collapsed(): boolean {
    return this._isCollapsed;
  }

  get isRightPositioned(): boolean {
    return this.position === 'right';
  }

  get collapseButtonIcon(): string {
    if (this.position === 'right') {
      return this._isCollapsed ? 'ArrowLeft' : 'ArrowRight';
    }
    return this._isCollapsed ? 'ArrowRight' : 'ArrowLeft';
  }

  private getDefaultWidth(): string {
    switch (this.size) {
      case 'small':
        return '200px';
      case 'medium':
        return '260px';
      case 'large':
        return '320px';
      default:
        return '260px';
    }
  }

  private getDefaultCollapsedWidth(): string {
    switch (this.size) {
      case 'small':
        return '48px';
      case 'medium':
        return '60px';
      case 'large':
        return '72px';
      default:
        return '60px';
    }
  }

  get sidebarClasses(): string[] {
    const classes = ['ava-sidebar'];

    if (this.collapsed) {
      classes.push('collapsed');
    }

    if (this.isRightPositioned) {
      classes.push('right-positioned');
    }

    classes.push(`size-${this.size}`);

    return classes;
  }

  get containerClasses(): string[] {
    const classes = ['ava-sidebar-container'];

    if (this.isRightPositioned) {
      classes.push('right-positioned');
    }

    if (this.buttonVariant === 'outside') {
      classes.push('outside-button');
    }

    if (this.collapsed) {
      classes.push('collapsed');
    }

    classes.push(`size-${this.size}`);

    return classes;
  }
}
