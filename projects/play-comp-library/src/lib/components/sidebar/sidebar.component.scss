$sidebar-border: var(--sidebar-border);

.ava-sidebar-container {
  display: flex;
  position: relative;
  width: fit-content;
  background: var(--sidebar-background);
  border-radius: var(--sidebar-border-radius);
  box-shadow: var(--sidebar-shadow);

  &.right-positioned {
    flex-direction: row-reverse;
  }

  // Size variants for container
  &.size-small {
    --current-sidebar-width: var(--sidebar-small-width);
    --current-collapsed-width: var(--sidebar-small-collapsed-width);
  }

  &.size-medium {
    --current-sidebar-width: var(--sidebar-medium-width);
    --current-collapsed-width: var(--sidebar-medium-collapsed-width);
  }

  &.size-large {
    --current-sidebar-width: var(--sidebar-large-width);
    --current-collapsed-width: var(--sidebar-large-collapsed-width);
  }
}

.ava-sidebar {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: var(--sidebar-transition);
  position: relative;
  border: $sidebar-border;
  background: var(--sidebar-background);
  border-radius: var(--sidebar-border-radius);

  &.collapsed {
    align-items: center;

    .header-content,
    .sidebar-footer {
      display: none;
    }

    .sidebar-header {
      justify-content: center;
      align-items: center;
      padding: var(--sidebar-collapsed-content-padding);
    }

    .sidebar-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: var(--sidebar-collapsed-content-padding);
    }
  }

  &.right-positioned {
    border-left: $sidebar-border;
    border-right: none;
  }

  // Size-specific styles
  &.size-small {
    .sidebar-header {
      padding: var(--sidebar-small-header-padding);
      min-height: var(--sidebar-small-header-min-height);
    }

    .sidebar-content {
      padding: var(--sidebar-small-content-padding);
    }

    .sidebar-footer {
      padding: var(--sidebar-small-footer-padding);
      min-height: var(--sidebar-small-footer-min-height);
    }
  }

  &.size-medium {
    .sidebar-header {
      padding: var(--sidebar-medium-header-padding);
      min-height: var(--sidebar-medium-header-min-height);
    }

    .sidebar-content {
      padding: var(--sidebar-medium-content-padding);
    }

    .sidebar-footer {
      padding: var(--sidebar-medium-footer-padding);
      min-height: var(--sidebar-medium-footer-min-height);
    }
  }

  &.size-large {
    .sidebar-header {
      padding: var(--sidebar-large-header-padding);
      min-height: var(--sidebar-large-header-min-height);
    }

    .sidebar-content {
      padding: var(--sidebar-large-content-padding);
    }

    .sidebar-footer {
      padding: var(--sidebar-large-footer-padding);
      min-height: var(--sidebar-large-footer-min-height);
    }
  }
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--sidebar-header-background);
  border-bottom: var(--sidebar-header-border-bottom);
  min-height: var(--sidebar-header-min-height);
  padding: var(--sidebar-header-padding);

  &.collapsed {
    justify-content: center;
  }
}

.header-controls {
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-content {
  flex: 1 1 auto;
  padding: var(--sidebar-content-padding);
  background: var(--sidebar-content-background);
  overflow-y: auto;
  overflow-x: hidden;
}

.sidebar-footer {
  background: var(--sidebar-footer-background);
  border-top: var(--sidebar-footer-border-top);
  min-height: var(--sidebar-footer-min-height);
  padding: var(--sidebar-footer-padding);
}

// Hover area for outside button variants
.ava-sidebar-container.outside-button .hover-area {
  position: absolute;
  top: 0;
  height: 100%;
  width: var(--hover-area-width, 50px);
  z-index: 10;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 8px;
  transition: var(--sidebar-hover-area-transition);
  opacity: 0;
  pointer-events: auto;
  background: var(--sidebar-hover-area-background);

  .hover-area-content {
    display: flex;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: var(--sidebar-hover-area-transition);
  }

  // Position the hover area in the white space next to the sidebar
  &.right-positioned {
    right: var(--sidebar-width, var(--current-sidebar-width));
  }

  &.left-positioned {
    left: var(--sidebar-width, var(--current-sidebar-width));
  }

  &:hover .hover-area-content {
    opacity: 1;
  }
}

// Show hover area button when hovering over sidebar container OR hover area itself
.ava-sidebar-container.outside-button:hover .hover-area,
.ava-sidebar-container.outside-button .hover-area:hover {
  opacity: 1;
  pointer-events: auto;

  .hover-area-content {
    opacity: 1;
  }
}

// Handle collapsed state positioning
.ava-sidebar-container.outside-button.collapsed .hover-area {
  &.left-positioned {
    left: var(--current-collapsed-width);
  }

  &.right-positioned {
    right: var(--current-collapsed-width);
  }
}

// Collapsed state styles
.ava-sidebar.collapsed {
  .sidebar-content {
    padding: var(--sidebar-collapsed-content-padding);
    align-items: center;

    // Generic navigation item styles for collapsed state
    .nav-item,
    .sidebar-nav-item {
      justify-content: center;
      width: var(--sidebar-collapsed-item-size);
      height: var(--sidebar-collapsed-item-size);
      margin: var(--sidebar-collapsed-item-margin) auto;

      .nav-text,
      .item-text,
      span:not(.icon):not(.ava-icon) {
        display: none;
      }
    }

    // Hide text in all collapsed items
    .demo-content,
    .user-info,
    .sidebar-section {
      justify-content: center;

      .nav-item span:not(.icon):not(.ava-icon),
      span:not(.icon):not(.ava-icon) {
        display: none;
      }
    }
  }

  // Center header content in collapsed state
  .sidebar-header {
    .demo-header-content,
    .header-content,
    .sidebar-header-content {
      justify-content: center;

      .header-title,
      .title,
      span:not(.icon):not(.ava-icon) {
        display: none;
      }
    }
  }

  // Center footer content in collapsed state
  .sidebar-footer {
    .footer-content,
    .user-info,
    .sidebar-footer-content {
      justify-content: center;

      .user-name,
      .footer-text,
      span:not(.icon):not(.ava-icon) {
        display: none;
      }
    }
  }
}

