<div [ngClass]="containerClasses"
     [style.--sidebar-width]="sidebarWidth"
     [style.--hover-area-width]="hoverAreaWidth"
     [style.--sidebar-height]="height">
  <!-- Main Sidebar -->

  <div
    [ngClass]="sidebarClasses"
    [style.width]="sidebarWidth"
    [style.height]="height"
    [attr.aria-label]="'Navigation sidebar'"
    [attr.aria-hidden]="collapsed"
  >
    <!-- Header Section -->

    <div class="sidebar-header" *ngIf="showHeader">
      <div class="header-content" *ngIf="!collapsed">
        <ng-content select="[slot=header]"></ng-content>
      </div>

      <!-- Inside Button Variant -->

      <div
        class="header-controls"
        *ngIf="showCollapseButton && buttonVariant === 'inside'"
      >
        <ava-button
        [iconName]="collapseButtonIcon"
        iconPosition="only"
        size="small"
        (click)="toggleCollapse()"
        variant="primary"
        [attr.aria-label]="collapsed ? 'Expand sidebar' : 'Collapse sidebar'"
        [attr.aria-expanded]="!collapsed"
        ></ava-button>
      </div>
    </div>

    <!-- Main Content Section -->

    <div class="sidebar-content">
      <ng-content select="[slot=content]"></ng-content>
    </div>

    <!-- Footer Section -->

    <div class="sidebar-footer" *ngIf="showFooter">
      <ng-content select="[slot=footer]"></ng-content>
    </div>
  </div>

  <!-- Outside Button Variant - Hover Area -->
  <div
    class="hover-area"
    [class.right-positioned]="isRightPositioned"
    [class.left-positioned]="!isRightPositioned"
    *ngIf="showCollapseButton && buttonVariant === 'outside'"
  >
    <div class="hover-area-content">
      <ava-button
        variant="primary"
        [iconName]="collapseButtonIcon"
        iconPosition="only"
        size="small"
        (click)="toggleCollapse()"
        [attr.aria-label]="collapsed ? 'Expand sidebar' : 'Collapse sidebar'"
        [attr.aria-expanded]="!collapsed"
      ></ava-button>
    </div>
  </div>
</div>
