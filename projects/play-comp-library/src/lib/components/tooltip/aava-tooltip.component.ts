import {
  Component,
  Input,
  ElementRef,
  Renderer2,
  ChangeDetectionStrategy,
  OnInit,
  ViewEncapsulation,
  ViewChild
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../icon/icon.component';


@Component({
  selector: 'aava-tooltip',
  imports: [CommonModule, IconComponent],
  templateUrl: './aava-tooltip.component.html',
  styleUrl: './aava-tooltip.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,

})
export class AavaTooltipComponent implements OnInit {
  @ViewChild('tooltip', { static: false }) tooltipEl!: ElementRef;
  @ViewChild('tooltipW', { static: false }) tooltipW!: ElementRef;

  @Input() config: any;
  visible = false;
  removeListeners: (() => void) | undefined;

  constructor(private hostEl: ElementRef, private renderer: Renderer2) { }

  ngOnInit(): void {
  }

  ngAfterViewInit() {
    setTimeout(() => {

      this.updatePosition();

      const onScrollOrResize = () => this.updatePosition();
      window.addEventListener('scroll', onScrollOrResize, true);
      window.addEventListener('resize', onScrollOrResize);

      this.removeListeners = () => {
        window.removeEventListener('scroll', onScrollOrResize, true);
        window.removeEventListener('resize', onScrollOrResize);
      };

      // this.renderer.addClass(tooltipEl, 'show');
      // this.renderer.removeClass(tooltipEl, 'show');

    }, 100);
  }
  updatePosition() {
    const tooltipWidth = this.tooltipEl.nativeElement.getBoundingClientRect().width;
    const toolTipheight = this.tooltipEl.nativeElement.getBoundingClientRect().height;
    if (this.config.position === 'top') {
      if (this.config.arrow === 'start') {
        const arrowPosition = this.config.left;
        const tooltipPosition = arrowPosition;
        this.tooltipW.nativeElement.style.left = `${tooltipPosition}px`;
        this.tooltipW.nativeElement.style.top = `${this.config.top - toolTipheight - 28}px`;

      } else if (this.config.arrow === 'center') {
        const arrowPosition = this.config.left + (this.config.width / 2);
        const tooltipPosition = arrowPosition - (tooltipWidth / 2);
        this.tooltipW.nativeElement.style.left = `${tooltipPosition}px`;
        this.tooltipW.nativeElement.style.top = `${this.config.top - toolTipheight - 28}px`;
      } else if (this.config.arrow === 'end') {
        const arrowPosition = this.config.left + this.config.width;
        const tooltipLeftPosition = arrowPosition - tooltipWidth;
        this.tooltipW.nativeElement.style.left = `${tooltipLeftPosition}px`;
        this.tooltipW.nativeElement.style.top = `${this.config.top - toolTipheight - 28}px`;
      }
    }
    if (this.config.position === 'left') {
      // Always center the tooltip body relative to the button - only arrow moves
      const tooltipPosition = this.config.top + (this.config.height / 2) - (toolTipheight / 2);
      this.tooltipW.nativeElement.style.top = `${tooltipPosition}px`;
      this.tooltipW.nativeElement.style.left = `${this.config.left - tooltipWidth - 30}px`;
    }
    if (this.config.position === 'right') {
      // Always center the tooltip body relative to the button - only arrow moves
      const tooltipPosition = this.config.top + (this.config.height / 2) - (toolTipheight / 2);
      this.tooltipW.nativeElement.style.top = `${tooltipPosition}px`;
      this.tooltipW.nativeElement.style.left = `${this.config.left + this.config.width + 10}px`;
    }
    if (this.config.position === 'bottom') {
      if (this.config.arrow === 'start') {
        const arrowPosition = this.config.left;
        const tooltipPosition = arrowPosition;
        this.tooltipW.nativeElement.style.left = `${tooltipPosition}px`;
        this.tooltipW.nativeElement.style.top = `${this.config.bottom + 20}px`;

      } else if (this.config.arrow === 'center') {
        const arrowPosition = this.config.left + (this.config.width / 2);
        const tooltipPosition = arrowPosition - (tooltipWidth / 2);
        this.tooltipW.nativeElement.style.left = `${tooltipPosition}px`;
        this.tooltipW.nativeElement.style.top = `${this.config.bottom + 20}px`;
      } else if (this.config.arrow === 'end') {
        const arrowPosition = this.config.left + this.config.width;
        const tooltipLeftPosition = arrowPosition - tooltipWidth;
        this.tooltipW.nativeElement.style.left = `${tooltipLeftPosition}px`;
        this.tooltipW.nativeElement.style.top = `${this.config.bottom + 20}px`;
      }
    }
    // Set arrow position after all positioning is complete
    this.setArrowPosition();

    this.tooltipW.nativeElement.style.opacity = `1`;
    this.visible = true;
  }

  setArrowPosition() {
    const tooltipWidth = this.tooltipEl.nativeElement.getBoundingClientRect().width;
    const tooltipHeight = this.tooltipEl.nativeElement.getBoundingClientRect().height;

    if (this.config.position === 'right' || this.config.position === 'left') {
      // For left/right positions, arrow moves vertically
      let arrowTopPosition = '50%'; // default center

      if (this.config.arrow === 'start') {
        // Arrow points to the start (top) of the button - position at top with 10px gap
        arrowTopPosition = '10px'; // Fixed position at top with 10px gap
      } else if (this.config.arrow === 'end') {
        // Arrow points to the end (bottom) of the button - position at bottom with 20px gap
        arrowTopPosition = `calc(100% - 20px)`; // Fixed position at bottom with 20px gap
      } else if (this.config.arrow === 'center') {
        arrowTopPosition = '50%';
      }

      this.tooltipW.nativeElement.style.setProperty('--arrow-top-position', arrowTopPosition, 'important');
      this.tooltipEl.nativeElement.style.setProperty('--arrow-top-position', arrowTopPosition, 'important');

    } else if (this.config.position === 'top' || this.config.position === 'bottom') {
      // For top/bottom positions, arrow moves horizontally
      let arrowLeftPosition = '50%'; // default center

      if (this.config.arrow === 'start') {
        // Arrow points to the start (left) of the button - position at left corner with 10px gap
        arrowLeftPosition = '10px'; // Fixed position at left corner
      } else if (this.config.arrow === 'end') {
        // Arrow points to the end (right) of the button - position at right corner with 20px gap
        arrowLeftPosition = `calc(100% - 20px)`; // Fixed position at right corner
      } else if (this.config.arrow === 'center') {
        arrowLeftPosition = '50%';
      }

      this.tooltipW.nativeElement.style.setProperty('--arrow-left-position', arrowLeftPosition);
      this.tooltipEl.nativeElement.style.setProperty('--arrow-left-position', arrowLeftPosition);
    }
  }



  ngOnDestroy() {
    this.removeListeners?.();
  }

}
 